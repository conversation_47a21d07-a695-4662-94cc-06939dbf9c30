"""
Simple enhanced logging without Unicode characters for Windows compatibility
"""
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
import sys

from models.data_models import OptionsData, ManipulationSignal

class SimpleTransparentLogger:
    """
    Enhanced logger that shows current prices and calculations without Unicode
    """
    
    def __init__(self, name: str = "options_detection"):
        self.logger = logging.getLogger(name)
        self.setup_logging()
        
    def setup_logging(self):
        """Setup enhanced logging with detailed formatting"""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler('options_detection_detailed.log')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.DEBUG)
    
    def log_current_prices(self, options_data: List[OptionsData]):
        """Log current market prices transparently"""
        if not options_data:
            self.logger.warning("[WARN] NO PRICE DATA AVAILABLE")
            return
        
        self.logger.info("[PRICES] CURRENT MARKET PRICES")
        self.logger.info("=" * 80)
        
        # Group by symbol
        symbols = {}
        for option in options_data:
            if option.symbol not in symbols:
                symbols[option.symbol] = {'calls': [], 'puts': []}
            
            if option.option_type.value == 'CE':
                symbols[option.symbol]['calls'].append(option)
            else:
                symbols[option.symbol]['puts'].append(option)
        
        for symbol, data in symbols.items():
            self.logger.info(f"[CHAIN] {symbol} OPTIONS CHAIN - {datetime.now().strftime('%H:%M:%S')}")
            
            # Show calls
            if data['calls']:
                self.logger.info("   CALL OPTIONS:")
                for call in sorted(data['calls'], key=lambda x: x.strike)[:5]:
                    self.logger.info(
                        f"     {call.strike:>7.0f} CE | "
                        f"Price: Rs{call.last_price:>6.2f} | "
                        f"Bid: Rs{call.bid_price:>6.2f} | "
                        f"Ask: Rs{call.ask_price:>6.2f} | "
                        f"Vol: {call.volume:>6,} | "
                        f"OI: {call.open_interest:>8,} | "
                        f"BidQ: {call.bid_qty:>4} | "
                        f"AskQ: {call.ask_qty:>4}"
                    )
            
            # Show puts
            if data['puts']:
                self.logger.info("   PUT OPTIONS:")
                for put in sorted(data['puts'], key=lambda x: x.strike)[:5]:
                    self.logger.info(
                        f"     {put.strike:>7.0f} PE | "
                        f"Price: Rs{put.last_price:>6.2f} | "
                        f"Bid: Rs{put.bid_price:>6.2f} | "
                        f"Ask: Rs{put.ask_price:>6.2f} | "
                        f"Vol: {put.volume:>6,} | "
                        f"OI: {put.open_interest:>8,} | "
                        f"BidQ: {put.bid_qty:>4} | "
                        f"AskQ: {put.ask_qty:>4}"
                    )
            
            self.logger.info("-" * 80)
    
    def log_manipulation_signal(self, signal: ManipulationSignal, 
                              calculation_breakdown: Dict[str, Any] = None):
        """Log manipulation signal with full transparency"""
        self.logger.warning("[ALERT] MANIPULATION SIGNAL DETECTED")
        self.logger.warning("=" * 80)
        self.logger.warning(f"   Pattern Type: {signal.pattern_type.value.upper()}")
        self.logger.warning(f"   Detection Time: {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.warning(f"   Confidence Level: {signal.confidence:.1%} ({signal.confidence_level.value.upper()})")
        self.logger.warning(f"   Symbols Affected: {', '.join(signal.symbols_affected)}")
        self.logger.warning(f"   Description: {signal.description}")
        
        # Financial impact
        self.logger.warning("[PROFIT] FINANCIAL IMPACT:")
        self.logger.warning(f"   Estimated Profit: Rs{signal.estimated_profit:,.2f}")
        
        # Market impact details
        self.logger.warning("[IMPACT] MARKET IMPACT:")
        for key, value in signal.market_impact.items():
            if isinstance(value, (int, float)):
                self.logger.warning(f"   {key}: {value:,.2f}")
            else:
                self.logger.warning(f"   {key}: {value}")
        
        # Calculation breakdown if provided
        if calculation_breakdown:
            self.logger.warning("[CALC] CALCULATION BREAKDOWN:")
            for key, value in calculation_breakdown.items():
                if isinstance(value, dict):
                    self.logger.warning(f"   {key}:")
                    for sub_key, sub_value in value.items():
                        self.logger.warning(f"     {sub_key}: {sub_value}")
                elif isinstance(value, (int, float)):
                    self.logger.warning(f"   {key}: {value:,.2f}")
                else:
                    self.logger.warning(f"   {key}: {value}")
        
        self.logger.warning("=" * 80)
    
    def log_detection_calculation(self, detector_name: str, data_points: int, 
                                calculation_details: Dict[str, Any]):
        """Log detection calculations transparently"""
        self.logger.info(f"[DETECT] DETECTION CALCULATION - {detector_name.upper()}")
        self.logger.info(f"   Data Points Analyzed: {data_points}")
        self.logger.info(f"   Calculation Time: {datetime.now().strftime('%H:%M:%S')}")
        
        # Log calculation details
        for key, value in calculation_details.items():
            if isinstance(value, (int, float)):
                self.logger.info(f"   {key}: {value:,.2f}")
            else:
                self.logger.info(f"   {key}: {value}")
    
    def log_price_movement(self, symbol: str, strike: float, option_type: str,
                          old_price: float, new_price: float, volume: int,
                          timestamp: datetime):
        """Log real-time price movements"""
        price_change = new_price - old_price
        price_change_pct = (price_change / old_price * 100) if old_price > 0 else 0
        
        direction = "[UP]" if price_change > 0 else "[DOWN]" if price_change < 0 else "[FLAT]"
        
        self.logger.info(f"[MOVE] PRICE MOVEMENT {direction}")
        self.logger.info(f"   Option: {symbol} {strike} {option_type}")
        self.logger.info(f"   Time: {timestamp.strftime('%H:%M:%S')}")
        self.logger.info(f"   Old Price: Rs{old_price:.2f}")
        self.logger.info(f"   New Price: Rs{new_price:.2f}")
        self.logger.info(f"   Change: Rs{price_change:+.2f} ({price_change_pct:+.2f}%)")
        self.logger.info(f"   Volume: {volume:,} lots")
    
    def log_nse_api_call(self, url: str, response_time: float, status_code: int,
                        data_points: int = 0, error: str = None):
        """Log NSE API calls with detailed information"""
        status_text = "[OK]" if status_code == 200 else "[ERROR]"
        
        self.logger.info(f"[API] NSE API CALL {status_text}")
        self.logger.info(f"   URL: {url}")
        self.logger.info(f"   Status: {status_code}")
        self.logger.info(f"   Response Time: {response_time:.3f}s")
        
        if status_code == 200:
            self.logger.info(f"   Data Points: {data_points}")
        else:
            self.logger.error(f"   Error: {error}")
    
    def log_detection_summary(self, cycle_number: int, total_signals: int,
                            high_confidence_signals: int, execution_time: float,
                            detectors_run: List[str]):
        """Log detection cycle summary"""
        self.logger.info("[CYCLE] DETECTION CYCLE SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"   Cycle Number: {cycle_number}")
        self.logger.info(f"   Execution Time: {execution_time:.2f}s")
        self.logger.info(f"   Detectors Run: {len(detectors_run)}")
        self.logger.info(f"   Total Signals: {total_signals}")
        self.logger.info(f"   High Confidence: {high_confidence_signals}")
        
        if high_confidence_signals > 0:
            self.logger.warning(f"[ALERT] {high_confidence_signals} HIGH CONFIDENCE SIGNALS REQUIRE ATTENTION!")
        
        self.logger.info(f"   Detectors: {', '.join(detectors_run)}")
        self.logger.info("=" * 60)
    
    def log_market_data_collection(self, symbol: str, data_count: int, timestamp: datetime):
        """Log market data collection with current status"""
        self.logger.info(f"[DATA] MARKET DATA COLLECTION")
        self.logger.info(f"   Symbol: {symbol}")
        self.logger.info(f"   Data Points: {data_count}")
        self.logger.info(f"   Collection Time: {timestamp.strftime('%H:%M:%S')}")
        self.logger.info(f"   Status: {'[OK] SUCCESS' if data_count > 0 else '[WARN] NO DATA'}")
    
    def log_system_status(self, status: Dict[str, Any]):
        """Log current system status"""
        self.logger.info("[SYSTEM] SYSTEM STATUS")
        self.logger.info(f"   Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        for component, details in status.items():
            if isinstance(details, dict):
                self.logger.info(f"   {component.upper()}:")
                for key, value in details.items():
                    self.logger.info(f"     {key}: {value}")
            else:
                self.logger.info(f"   {component}: {details}")

# Global simple logger instance
simple_logger = SimpleTransparentLogger()

# Convenience functions
def log_current_prices(options_data: List[OptionsData]):
    """Log current market prices"""
    simple_logger.log_current_prices(options_data)

def log_manipulation_signal(signal: ManipulationSignal, calculation_breakdown: Dict[str, Any] = None):
    """Log manipulation signal"""
    simple_logger.log_manipulation_signal(signal, calculation_breakdown)

def log_detection_summary(cycle_number: int, total_signals: int, high_confidence_signals: int,
                         execution_time: float, detectors_run: List[str]):
    """Log detection cycle summary"""
    simple_logger.log_detection_summary(cycle_number, total_signals, high_confidence_signals, execution_time, detectors_run)
