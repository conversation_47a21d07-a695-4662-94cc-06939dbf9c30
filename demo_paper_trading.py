#!/usr/bin/env python3
"""
Comprehensive demonstration of the Real-Time Paper Trading System
"""
import asyncio
import requests
import json
import time
from datetime import datetime, timed<PERSON>ta

def print_banner():
    """Print paper trading banner"""
    print("=" * 80)
    print("📈 REAL-TIME PAPER TRADING SYSTEM - LIVE DEMONSTRATION")
    print("=" * 80)
    print("💰 Simulated trading based on detected manipulation signals")
    print("📊 Real-time P&L tracking and performance analytics")
    print("🎯 Proof of system effectiveness without real money risk")
    print("=" * 80)
    print()

def test_paper_trading_endpoints():
    """Test all paper trading API endpoints"""
    base_url = "http://localhost:8081"
    
    print("🌐 TESTING PAPER TRADING ENDPOINTS")
    print("-" * 50)
    
    endpoints = [
        ("/paper-trading/status", "Paper trading status"),
        ("/paper-trading/performance", "Performance summary"),
        ("/paper-trading/trades", "Recent trades"),
        ("/paper-trading/analytics", "Trading analytics")
    ]
    
    for endpoint, description in endpoints:
        try:
            print(f"📡 Testing {endpoint} - {description}")
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ SUCCESS - {data.get('message', 'OK')}")
                
                # Show key data for interesting endpoints
                if endpoint == "/paper-trading/status":
                    status_data = data.get('data', {})
                    performance = status_data.get('performance', {})
                    print(f"      💰 Current Capital: ₹{performance.get('current_capital', 0):,.0f}")
                    print(f"      📈 Total P&L: ₹{performance.get('total_profit_loss', 0):,.0f}")
                    print(f"      🎯 Win Rate: {performance.get('win_rate', 0):.1f}%")
                    print(f"      📊 Total Trades: {performance.get('total_trades', 0)}")
                
                elif endpoint == "/paper-trading/trades":
                    trades_data = data.get('data', {})
                    trades = trades_data.get('trades', [])
                    print(f"      📋 Recent Trades: {len(trades)}")
                    
                    # Show top 3 most profitable trades
                    if trades:
                        profitable_trades = sorted(trades, key=lambda x: x.get('actual_profit', 0), reverse=True)[:3]
                        print("      🏆 Top Profitable Trades:")
                        for i, trade in enumerate(profitable_trades, 1):
                            profit = trade.get('actual_profit', 0)
                            symbol = trade.get('symbol', 'Unknown')
                            action = trade.get('action', 'Unknown')
                            print(f"         {i}. {action} {symbol}: ₹{profit:,.0f}")
                
                elif endpoint == "/paper-trading/analytics":
                    analytics_data = data.get('data', {})
                    overall = analytics_data.get('overall_performance', {})
                    risk = analytics_data.get('risk_analysis', {})
                    print(f"      📊 ROI: {overall.get('total_profit_loss_percent', 0):.2f}%")
                    print(f"      📈 Best Trade: ₹{risk.get('largest_gain', 0):,.0f}")
                    print(f"      📉 Worst Trade: ₹{risk.get('largest_loss', 0):,.0f}")
                    print(f"      ⏱️  Avg Duration: {risk.get('average_trade_duration', 0):.1f} min")
                    
            else:
                print(f"   ❌ FAILED - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR - {str(e)}")
        
        print()

def trigger_detection_and_trading():
    """Trigger detection cycle to generate new trading opportunities"""
    print("🔄 TRIGGERING DETECTION CYCLE FOR NEW TRADES")
    print("-" * 50)
    
    try:
        # Trigger detection cycle
        response = requests.post("http://localhost:8081/detection/run", timeout=15)
        
        if response.status_code == 200:
            print("✅ Detection cycle triggered successfully")
            
            # Wait for detection to complete
            print("⏳ Waiting for detection and trading to complete...")
            time.sleep(5)
            
            # Check for new trades
            trades_response = requests.get("http://localhost:8081/paper-trading/trades?hours=1", timeout=5)
            if trades_response.status_code == 200:
                trades_data = trades_response.json()
                trades = trades_data.get('data', {}).get('trades', [])
                
                print(f"📊 Detection and trading cycle completed")
                print(f"🎯 Recent trades (last hour): {len(trades)}")
                
                if trades:
                    print("\n📋 Latest Paper Trades:")
                    for i, trade in enumerate(trades[:5], 1):  # Show top 5
                        symbol = trade.get('symbol', 'Unknown')
                        action = trade.get('action', 'Unknown')
                        confidence = trade.get('confidence', 0) * 100
                        pnl = trade.get('profit_loss', 0)
                        status = trade.get('status', 'Unknown')
                        
                        print(f"   {i}. {action} {symbol}")
                        print(f"      Status: {status}")
                        print(f"      Confidence: {confidence:.1f}%")
                        print(f"      Current P&L: ₹{pnl:,.0f}")
                        print()
                else:
                    print("ℹ️  No new trades generated from recent signals")
            
        else:
            print(f"❌ Failed to trigger detection cycle - HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error triggering detection: {str(e)}")
    
    print()

def show_live_performance():
    """Show live performance dashboard"""
    print("📊 LIVE PERFORMANCE DASHBOARD")
    print("-" * 50)
    
    try:
        # Get current performance
        response = requests.get("http://localhost:8081/paper-trading/performance", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            performance_data = data.get('data', {})
            overall = performance_data.get('overall_performance', {})
            weekly = performance_data.get('weekly_performance', {})
            risk = performance_data.get('risk_metrics', {})
            
            print("💰 CAPITAL SUMMARY:")
            print(f"   Initial Capital: ₹{overall.get('initial_capital', 0):,.0f}")
            print(f"   Current Capital: ₹{overall.get('current_capital', 0):,.0f}")
            print(f"   Available Capital: ₹{overall.get('available_capital', 0):,.0f}")
            print(f"   Total P&L: ₹{overall.get('total_profit_loss', 0):,.0f}")
            print(f"   ROI: {overall.get('total_profit_loss_percent', 0):.2f}%")
            print()
            
            print("📈 TRADING STATISTICS:")
            print(f"   Total Trades: {overall.get('total_trades', 0)}")
            print(f"   Open Positions: {overall.get('open_trades', 0)}")
            print(f"   Winning Trades: {overall.get('winning_trades', 0)}")
            print(f"   Losing Trades: {overall.get('losing_trades', 0)}")
            print(f"   Win Rate: {overall.get('win_rate', 0):.1f}%")
            print(f"   Avg Profit/Trade: ₹{overall.get('average_profit_per_trade', 0):,.0f}")
            print()
            
            print("📊 WEEKLY PERFORMANCE:")
            print(f"   Weekly Trades: {weekly.get('trades_count', 0)}")
            print(f"   Weekly P&L: ₹{weekly.get('total_pnl', 0):,.0f}")
            print(f"   Avg P&L/Trade: ₹{weekly.get('average_pnl', 0):,.0f}")
            print()
            
            print("⚠️  RISK METRICS:")
            print(f"   Current Exposure: ₹{risk.get('current_exposure', 0):,.0f}")
            print(f"   Max Position Size: {risk.get('max_position_size_percent', 0):.1f}%")
            print(f"   Stop Loss: {risk.get('stop_loss_percent', 0):.1f}%")
            print(f"   Take Profit: {risk.get('take_profit_percent', 0):.1f}%")
            
        else:
            print(f"❌ Failed to get performance data - HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting performance data: {str(e)}")
    
    print()

def show_trading_analytics():
    """Show detailed trading analytics"""
    print("🔍 DETAILED TRADING ANALYTICS")
    print("-" * 50)
    
    try:
        response = requests.get("http://localhost:8081/paper-trading/analytics", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            analytics_data = data.get('data', {})
            manipulation_stats = analytics_data.get('manipulation_type_analysis', {})
            daily_pnl = analytics_data.get('daily_pnl', {})
            risk_analysis = analytics_data.get('risk_analysis', {})
            
            print("🎯 MANIPULATION TYPE PERFORMANCE:")
            for m_type, stats in manipulation_stats.items():
                print(f"   {m_type.upper()}:")
                print(f"     Trades: {stats.get('count', 0)}")
                print(f"     Total P&L: ₹{stats.get('total_pnl', 0):,.0f}")
                print(f"     Avg P&L: ₹{stats.get('average_pnl', 0):,.0f}")
                print(f"     Win Rate: {stats.get('win_rate', 0):.1f}%")
                print(f"     Avg Confidence: {stats.get('average_confidence', 0):.1f}%")
                print()
            
            print("📅 DAILY P&L BREAKDOWN:")
            for date, pnl in sorted(daily_pnl.items())[-7:]:  # Last 7 days
                print(f"   {date}: ₹{pnl:,.0f}")
            print()
            
            print("⚠️  RISK ANALYSIS:")
            print(f"   Largest Loss: ₹{risk_analysis.get('largest_loss', 0):,.0f}")
            print(f"   Largest Gain: ₹{risk_analysis.get('largest_gain', 0):,.0f}")
            print(f"   Avg Trade Duration: {risk_analysis.get('average_trade_duration', 0):.1f} minutes")
            print(f"   Current Open Positions: {risk_analysis.get('current_open_positions', 0)}")
            
        else:
            print(f"❌ Failed to get analytics data - HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting analytics data: {str(e)}")
    
    print()

def main():
    """Main demonstration function"""
    print_banner()
    
    # Test API endpoints
    test_paper_trading_endpoints()
    
    # Show current performance
    show_live_performance()
    
    # Trigger new detection and trading
    trigger_detection_and_trading()
    
    # Show updated performance
    show_live_performance()
    
    # Show detailed analytics
    show_trading_analytics()
    
    # Final summary
    print("🎉 PAPER TRADING DEMONSTRATION COMPLETE")
    print("-" * 50)
    print("✅ Real-time paper trading system operational")
    print("📈 Live P&L tracking and performance metrics")
    print("🎯 Automated trading based on manipulation signals")
    print("📊 Comprehensive analytics and risk management")
    print()
    print("🚀 The system is now ready to prove its effectiveness!")
    print("   Every detected manipulation signal is automatically")
    print("   converted into a paper trade with real-time P&L tracking.")
    print()

if __name__ == "__main__":
    main()
