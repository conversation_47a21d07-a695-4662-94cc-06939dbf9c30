#!/usr/bin/env python3
"""
Test real data collection from multiple sources
"""
import asyncio
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_nse_direct():
    """Test NSE API directly"""
    print("🔍 TESTING NSE API DIRECTLY")
    print("-" * 50)
    
    try:
        from data_collectors.nse_collector import NSEDataCollector
        
        collector = NSEDataCollector()
        await collector.start_session()
        
        print("✅ NSE collector initialized")
        
        # Test data collection
        options_data = await collector.get_options_chain('NIFTY')
        print(f"📊 Collected {len(options_data)} options from NSE")
        
        if options_data:
            sample = options_data[0]
            print(f"📈 Sample: {sample.symbol} {sample.strike} {sample.option_type.value} @ ₹{sample.last_price}")
        
        await collector.close_session()
        return True
        
    except Exception as e:
        print(f"❌ NSE API failed: {str(e)}")
        return False

async def test_yahoo_direct():
    """Test Yahoo Finance API directly"""
    print("\n🔍 TESTING YAHOO FINANCE API DIRECTLY")
    print("-" * 50)
    
    try:
        from data_collectors.yahoo_collector import YahooFinanceCollector
        
        collector = YahooFinanceCollector()
        await collector.start_session()
        
        print("✅ Yahoo Finance collector initialized")
        
        # Test data collection
        options_data = await collector.get_options_chain('NIFTY')
        print(f"📊 Collected {len(options_data)} options from Yahoo Finance")
        
        if options_data:
            sample = options_data[0]
            print(f"📈 Sample: {sample.symbol} {sample.strike} {sample.option_type.value} @ ${sample.last_price}")
        
        await collector.close_session()
        return True
        
    except Exception as e:
        print(f"❌ Yahoo Finance API failed: {str(e)}")
        return False

async def test_multi_source():
    """Test multi-source collector"""
    print("\n🔍 TESTING MULTI-SOURCE COLLECTOR")
    print("-" * 50)
    
    try:
        from data_collectors.multi_source_collector import MultiSourceDataCollector
        
        collector = MultiSourceDataCollector()
        await collector.initialize()
        
        print("✅ Multi-source collector initialized")
        print(f"🌐 Active sources: {collector.active_sources}")
        
        # Test source availability
        test_results = await collector.test_all_sources()
        print(f"🧪 Source test results: {test_results}")
        
        # Test data collection
        symbols = ['NIFTY']
        data = await collector.collect_all_symbols(symbols)
        
        total_options = sum(len(options) for options in data.values())
        print(f"📊 Collected {total_options} total options from all sources")
        
        for symbol, options in data.items():
            if options:
                sample = options[0]
                print(f"📈 {symbol} sample: {sample.strike} {sample.option_type.value} @ {sample.last_price}")
        
        # Show statistics
        stats = collector.get_statistics()
        print(f"📈 Collection statistics: {stats['multi_source_stats']}")
        
        await collector.shutdown()
        return len(data.get('NIFTY', [])) > 0
        
    except Exception as e:
        print(f"❌ Multi-source collector failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 TESTING REAL DATA COLLECTION SYSTEMS")
    print("=" * 60)
    
    results = {}
    
    # Test NSE directly
    results['nse'] = await test_nse_direct()
    
    # Test Yahoo Finance directly
    results['yahoo'] = await test_yahoo_direct()
    
    # Test multi-source collector
    results['multi_source'] = await test_multi_source()
    
    # Summary
    print("\n🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    working_sources = []
    for source, success in results.items():
        status = "✅ WORKING" if success else "❌ FAILED"
        print(f"{source.upper()}: {status}")
        if success:
            working_sources.append(source)
    
    print(f"\n🌟 Working data sources: {working_sources}")
    
    if working_sources:
        print("🎉 SUCCESS! At least one data source is working!")
        print("🚀 The system can now collect REAL market data!")
    else:
        print("😞 All data sources failed. Check network connectivity and API access.")
    
    return len(working_sources) > 0

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
