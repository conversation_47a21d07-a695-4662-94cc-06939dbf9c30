#!/usr/bin/env python3
"""
Test paper trading system with mock data to demonstrate functionality
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List
import uuid

from models.data_models import ManipulationSignal, OptionsData, PatternType, OptionType
from paper_trading.paper_trader import paper_trading_engine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_mock_options_data() -> List[OptionsData]:
    """Create mock options data for testing"""
    mock_data = []
    
    # NIFTY options at different strikes
    strikes = [23000, 24000, 25000, 26000, 27000]
    
    for strike in strikes:
        # Call option
        call_option = OptionsData(
            symbol="NIFTY",
            expiry_date=datetime.now() + timedelta(days=7),
            strike=float(strike),
            option_type=OptionType.CE,
            last_price=50.0 + (strike - 25000) * 0.01,  # Varying prices
            bid_price=49.0 + (strike - 25000) * 0.01,
            ask_price=51.0 + (strike - 25000) * 0.01,
            volume=1000,
            open_interest=5000,
            bid_qty=100,
            ask_qty=100,
            timestamp=datetime.now()
        )
        mock_data.append(call_option)
        
        # Put option
        put_option = OptionsData(
            symbol="NIFTY",
            expiry_date=datetime.now() + timedelta(days=7),
            strike=float(strike),
            option_type=OptionType.PE,
            last_price=45.0 + (25000 - strike) * 0.01,  # Inverse pricing for puts
            bid_price=44.0 + (25000 - strike) * 0.01,
            ask_price=46.0 + (25000 - strike) * 0.01,
            volume=800,
            open_interest=4000,
            bid_qty=150,
            ask_qty=150,
            timestamp=datetime.now()
        )
        mock_data.append(put_option)
    
    return mock_data

def create_mock_manipulation_signals() -> List[ManipulationSignal]:
    """Create mock manipulation signals for testing"""
    signals = []
    
    # High confidence bid spoofing signal
    signal1 = ManipulationSignal(
        id=str(uuid.uuid4()),
        pattern_type=PatternType.ORDER_SPOOFING,
        timestamp=datetime.now(),
        symbols_affected=["NIFTY_25000.0_CE"],
        confidence=0.95,
        description="Bid spoofing detected: 2,000 lots added and removed within 0.5s on NIFTY 25000.0 CE",
        estimated_profit=5000000,  # ₹50 lakhs
        market_impact={
            "spoof_type": "bid_spoofing",
            "price_impact": 25.0,
            "quantity_impact": 2000,
            "time_window": 0.5
        }
    )
    signals.append(signal1)
    
    # High confidence ask spoofing signal
    signal2 = ManipulationSignal(
        id=str(uuid.uuid4()),
        pattern_type=PatternType.ORDER_SPOOFING,
        timestamp=datetime.now(),
        symbols_affected=["NIFTY_24000.0_PE"],
        confidence=0.92,
        description="Ask spoofing detected: 1,500 lots added and removed within 0.3s on NIFTY 24000.0 PE",
        estimated_profit=3500000,  # ₹35 lakhs
        market_impact={
            "spoof_type": "ask_spoofing",
            "price_impact": 18.0,
            "quantity_impact": 1500,
            "time_window": 0.3
        }
    )
    signals.append(signal2)
    
    # Medium confidence coordinated spoofing
    signal3 = ManipulationSignal(
        id=str(uuid.uuid4()),
        pattern_type=PatternType.ORDER_SPOOFING,
        timestamp=datetime.now(),
        symbols_affected=["NIFTY_26000.0_CE"],
        confidence=0.85,
        description="Coordinated bid-ask spoofing detected within 1.0s on NIFTY 26000.0 CE",
        estimated_profit=2000000,  # ₹20 lakhs
        market_impact={
            "spoof_type": "coordinated_spoofing",
            "price_impact": 12.0,
            "quantity_impact": 1000,
            "time_window": 1.0
        }
    )
    signals.append(signal3)
    
    # Lower confidence signal (should not trigger trade)
    signal4 = ManipulationSignal(
        id=str(uuid.uuid4()),
        pattern_type=PatternType.ORDER_SPOOFING,
        timestamp=datetime.now(),
        symbols_affected=["NIFTY_23000.0_PE"],
        confidence=0.75,  # Below threshold
        description="Potential bid spoofing detected on NIFTY 23000.0 PE",
        estimated_profit=1000000,  # ₹10 lakhs
        market_impact={
            "spoof_type": "bid_spoofing",
            "price_impact": 8.0,
            "quantity_impact": 500,
            "time_window": 2.0
        }
    )
    signals.append(signal4)
    
    return signals

async def simulate_price_movements(mock_data: List[OptionsData]) -> List[OptionsData]:
    """Simulate price movements for testing trade updates"""
    updated_data = []
    
    for option in mock_data:
        # Simulate price movement based on manipulation type
        price_change = 0
        
        if option.strike == 25000 and option.option_type == OptionType.CE:
            # Simulate price drop after bid spoofing (reversion)
            price_change = -3.0
        elif option.strike == 24000 and option.option_type == OptionType.PE:
            # Simulate price rise after ask spoofing (reversion)
            price_change = 2.5
        elif option.strike == 26000 and option.option_type == OptionType.CE:
            # Simulate moderate movement
            price_change = 1.5
        else:
            # Random small movements for other options
            import random
            price_change = random.uniform(-1.0, 1.0)
        
        # Create updated option data
        updated_option = OptionsData(
            symbol=option.symbol,
            expiry_date=option.expiry_date,
            strike=option.strike,
            option_type=option.option_type,
            last_price=max(0.1, option.last_price + price_change),  # Ensure positive price
            bid_price=max(0.1, option.bid_price + price_change),
            ask_price=max(0.1, option.ask_price + price_change),
            volume=option.volume + 100,  # Simulate increased volume
            open_interest=option.open_interest,
            bid_qty=option.bid_qty,
            ask_qty=option.ask_qty,
            timestamp=datetime.now()
        )
        updated_data.append(updated_option)
    
    return updated_data

async def test_paper_trading_system():
    """Test the complete paper trading system"""
    print("🚀 TESTING PAPER TRADING SYSTEM WITH MOCK DATA")
    print("=" * 60)
    
    try:
        # Create mock data
        print("📊 Creating mock options data...")
        mock_options = create_mock_options_data()
        print(f"   Created {len(mock_options)} mock options")
        
        print("\n🚨 Creating mock manipulation signals...")
        mock_signals = create_mock_manipulation_signals()
        print(f"   Created {len(mock_signals)} mock signals")
        
        # Show initial paper trading status
        initial_performance = paper_trading_engine.get_performance_summary()
        print(f"\n💰 Initial Capital: ₹{initial_performance['initial_capital']:,.0f}")
        print(f"📊 Initial Trades: {initial_performance['total_trades']}")
        
        # Process signals for paper trading
        print("\n🔄 Processing signals for paper trading...")
        trades_executed = 0
        
        for signal in mock_signals:
            print(f"\n📈 Processing signal: {signal.description}")
            print(f"   Confidence: {signal.confidence:.1%}")
            print(f"   Estimated Profit: ₹{signal.estimated_profit:,.0f}")
            
            trade = await paper_trading_engine.process_manipulation_signal(signal, mock_options)
            
            if trade:
                trades_executed += 1
                print(f"   ✅ TRADE EXECUTED: {trade.action} {trade.quantity} lots of {trade.symbol}")
                print(f"   Entry Price: ₹{trade.entry_price}")
                print(f"   Trade Value: ₹{trade.quantity * trade.entry_price * 50:,.0f}")
            else:
                print(f"   ❌ No trade executed (confidence too low or other criteria not met)")
        
        print(f"\n📊 Total trades executed: {trades_executed}")
        
        # Show current open trades
        if paper_trading_engine.open_trades:
            print(f"\n📋 Open Trades ({len(paper_trading_engine.open_trades)}):")
            for trade_id, trade in paper_trading_engine.open_trades.items():
                print(f"   • {trade.action} {trade.symbol}: {trade.quantity} lots @ ₹{trade.entry_price}")
                print(f"     Current P&L: ₹{trade.profit_loss:,.0f} ({trade.profit_loss_percent:.1%})")
        
        # Simulate market movements and update trades
        print(f"\n⏰ Simulating market movements...")
        updated_options = await simulate_price_movements(mock_options)
        
        # Update open trades with new market data
        await paper_trading_engine.update_open_trades(updated_options)
        
        # Show updated performance
        print(f"\n📈 Updated Trade Performance:")
        for trade_id, trade in paper_trading_engine.open_trades.items():
            print(f"   • {trade.action} {trade.symbol}:")
            print(f"     Entry: ₹{trade.entry_price} → Current: ₹{trade.profit_loss / (trade.quantity * 50) + trade.entry_price:.2f}")
            print(f"     P&L: ₹{trade.profit_loss:,.0f} ({trade.profit_loss_percent:.1%})")
            print(f"     Status: {trade.status}")
        
        # Show final performance summary
        final_performance = paper_trading_engine.get_performance_summary()
        print(f"\n🎯 FINAL PERFORMANCE SUMMARY:")
        print(f"   Initial Capital: ₹{final_performance['initial_capital']:,.0f}")
        print(f"   Current Capital: ₹{final_performance['current_capital']:,.0f}")
        print(f"   Total P&L: ₹{final_performance['total_profit_loss']:,.0f}")
        print(f"   ROI: {final_performance['total_profit_loss_percent']:.2f}%")
        print(f"   Total Trades: {final_performance['total_trades']}")
        print(f"   Open Trades: {final_performance['open_trades']}")
        print(f"   Win Rate: {final_performance['win_rate']:.1f}%")
        
        # Show closed trades if any
        if paper_trading_engine.closed_trades:
            print(f"\n📋 Closed Trades ({len(paper_trading_engine.closed_trades)}):")
            for trade in paper_trading_engine.closed_trades:
                print(f"   • {trade.action} {trade.symbol}: ₹{trade.actual_profit:,.0f}")
                print(f"     Duration: {(trade.exit_time - trade.entry_time).total_seconds() / 60:.1f} min")
        
        print(f"\n✅ Paper trading system test completed successfully!")
        print(f"🎉 System is ready to trade on real manipulation signals!")
        
    except Exception as e:
        logger.error(f"Error in paper trading test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_paper_trading_system())
