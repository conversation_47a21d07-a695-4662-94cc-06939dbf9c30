#!/usr/bin/env python3
"""
Run only the detection engine without the API server
"""
import asyncio
import logging
import signal
import sys
import os
from datetime import datetime

# Set UTF-8 encoding for Windows console
if sys.platform == "win32":
    os.environ["PYTHONIOENCODING"] = "utf-8"

from core.detection_engine import detection_engine
from config.settings import settings

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=getattr(logging, settings.monitoring.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('options_detection.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class DetectionOnlySystem:
    """
    Detection-only system orchestrator
    """
    
    def __init__(self):
        self.running = False
        self.shutdown_event = asyncio.Event()
    
    async def start(self):
        """Start the detection system"""
        try:
            logger.info("🚀 Starting Options Manipulation Detection Engine (Detection Only)")
            logger.info(f"Environment: {settings.environment}")
            logger.info(f"Monitoring symbols: {settings.symbols}")
            logger.info(f"Detection interval: {settings.detection_interval} seconds")
            
            # Initialize detection engine
            await detection_engine.initialize()
            
            # Set up signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            self.running = True
            logger.info("✅ Detection engine started successfully")
            
            # Run detection engine
            await detection_engine.run_continuous()
            
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested by user")
        except Exception as e:
            logger.error(f"❌ Fatal error: {str(e)}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown the system gracefully"""
        if not self.running:
            return
        
        logger.info("🔄 Shutting down Detection Engine")
        self.running = False
        
        try:
            # Shutdown detection engine
            await detection_engine.shutdown()
            
            # Set shutdown event
            self.shutdown_event.set()
            
            logger.info("✅ Detection engine shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {str(e)}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"📡 Received signal {signum}, initiating shutdown")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Main entry point"""
    system = DetectionOnlySystem()
    await system.start()

if __name__ == "__main__":
    try:
        print("=" * 80)
        print("🔍 OPTIONS MANIPULATION DETECTION SYSTEM")
        print("📊 Detection Engine Only Mode")
        print("=" * 80)
        print()
        
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n🛑 System stopped by user")
    except Exception as e:
        print(f"\n❌ System failed: {str(e)}")
        sys.exit(1)
