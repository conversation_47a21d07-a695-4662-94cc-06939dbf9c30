"""
Simple test to verify basic functionality without complex dependencies
"""
import asyncio
from datetime import datetime
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test that we can import basic modules"""
    try:
        from models.data_models import OptionsData, OptionType, PatternType, ManipulationSignal
        print("✅ Successfully imported data models")
        return True
    except Exception as e:
        print(f"❌ Failed to import data models: {e}")
        return False

def test_data_model_creation():
    """Test creating data model instances"""
    try:
        from models.data_models import OptionsData, OptionType, PatternType, ManipulationSignal
        
        # Test OptionsData creation
        option = OptionsData(
            symbol="NIFTY",
            expiry_date=datetime(2024, 1, 25),
            strike=21000.0,
            option_type=OptionType.CALL,
            last_price=150.0,
            bid_price=149.0,
            ask_price=151.0,
            volume=1000,
            open_interest=5000,
            bid_qty=100,
            ask_qty=200,
            timestamp=datetime.now()
        )
        
        assert option.symbol == "NIFTY"
        assert option.option_type == OptionType.CALL
        print("✅ OptionsData model works correctly")
        
        # Test ManipulationSignal creation
        signal = ManipulationSignal(
            pattern_type=PatternType.ORDER_SPOOFING,
            timestamp=datetime.now(),
            symbols_affected=["NIFTY_21000_CE"],
            confidence=0.85,
            description="Test spoofing signal",
            estimated_profit=50000.0,
            market_impact={"price_move": 2.5}
        )
        
        assert signal.pattern_type == PatternType.ORDER_SPOOFING
        assert signal.confidence == 0.85
        print("✅ ManipulationSignal model works correctly")
        
        return True
    except Exception as e:
        print(f"❌ Failed to create data models: {e}")
        return False

def test_nse_collector_basic():
    """Test basic NSE collector functionality"""
    try:
        from data_collectors.nse_collector import NSEDataCollector
        
        # Just test initialization
        collector = NSEDataCollector()
        print("✅ NSE collector can be initialized")
        
        return True
    except Exception as e:
        print(f"❌ Failed to initialize NSE collector: {e}")
        return False

def test_detection_algorithms():
    """Test detection algorithm initialization"""
    try:
        from detection.spoofing_detector import SpoofingDetector
        from detection.gamma_squeeze_detector import GammaSqueezeDetector
        
        # Test detector initialization
        spoofing_detector = SpoofingDetector()
        gamma_detector = GammaSqueezeDetector()
        
        assert spoofing_detector.name == "spoofing_detector"
        assert gamma_detector.name == "gamma_squeeze_detector"
        
        print("✅ Detection algorithms can be initialized")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize detection algorithms: {e}")
        return False

async def test_async_functionality():
    """Test async functionality"""
    try:
        from detection.spoofing_detector import SpoofingDetector
        from models.data_models import OptionsData, OptionType
        
        # Create sample data
        options_data = []
        base_time = datetime.now()
        
        for i in range(25):  # Enough data for detection
            option = OptionsData(
                symbol="NIFTY",
                expiry_date=datetime(2024, 1, 25),
                strike=21000.0,
                option_type=OptionType.CALL,
                last_price=150.0,
                bid_price=149.0,
                ask_price=151.0,
                volume=100,
                open_interest=5000,
                bid_qty=100,
                ask_qty=200,
                timestamp=base_time
            )
            options_data.append(option)
        
        # Test detection
        detector = SpoofingDetector()
        signals = await detector.detect(options_data)
        
        assert isinstance(signals, list)
        print("✅ Async detection functionality works")
        
        return True
    except Exception as e:
        print(f"❌ Failed async functionality test: {e}")
        return False

def test_nse_api_connection():
    """Test actual NSE API connection"""
    try:
        import requests
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json'
        }
        
        # Test NSE market status endpoint (lightweight)
        url = "https://www.nseindia.com/api/marketStatus"
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ NSE API connection successful")
            print(f"   Response size: {len(response.text)} bytes")
            return True
        else:
            print(f"⚠️  NSE API returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"⚠️  NSE API connection failed: {e}")
        print("   This is normal if you don't have internet or NSE is blocking requests")
        return False

def main():
    """Run all tests"""
    print("🧪 Running Simple Functionality Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Data Model Creation", test_data_model_creation),
        ("NSE Collector Basic", test_nse_collector_basic),
        ("Detection Algorithms", test_detection_algorithms),
        ("NSE API Connection", test_nse_api_connection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Test async functionality separately
    print(f"\n🔍 Testing: Async Functionality")
    try:
        result = asyncio.run(test_async_functionality())
        results.append(("Async Functionality", result))
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        results.append(("Async Functionality", False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is working correctly.")
        return True
    elif passed >= total * 0.7:  # 70% pass rate
        print("⚠️  Most tests passed. Some optional features may not work.")
        return True
    else:
        print("❌ Many tests failed. Please check the setup.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
