#!/usr/bin/env python3
"""
Database models for paper trading system
"""
from sqlalchemy import Column, String, Float, Integer, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class PaperTradeRecord(Base):
    """Database model for paper trades"""
    __tablename__ = "paper_trades"
    
    # Primary identification
    id = Column(String, primary_key=True)
    signal_id = Column(String, nullable=False)
    
    # Trade details
    symbol = Column(String, nullable=False)
    strike = Column(Float, nullable=False)
    option_type = Column(String, nullable=False)  # CE/PE
    action = Column(String, nullable=False)  # BUY/SELL
    
    # Pricing and quantity
    entry_price = Column(Float, nullable=False)
    exit_price = Column(Float, nullable=True)
    quantity = Column(Integer, nullable=False)
    
    # Timing
    entry_time = Column(DateTime, nullable=False, default=datetime.now)
    exit_time = Column(DateTime, nullable=True)
    
    # Status and results
    status = Column(String, nullable=False, default="OPEN")  # OPEN/CLOSED/EXPIRED
    profit_loss = Column(Float, nullable=False, default=0.0)
    profit_loss_percent = Column(Float, nullable=False, default=0.0)
    actual_profit = Column(Float, nullable=False, default=0.0)
    
    # Signal information
    confidence = Column(Float, nullable=False)
    manipulation_type = Column(String, nullable=False)
    estimated_profit = Column(Float, nullable=False, default=0.0)
    trade_reason = Column(Text, nullable=True)
    
    # Market data at entry
    market_data = Column(JSON, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

class PaperTradingSession(Base):
    """Database model for paper trading sessions"""
    __tablename__ = "paper_trading_sessions"
    
    # Session identification
    id = Column(String, primary_key=True)
    session_name = Column(String, nullable=False)
    
    # Capital tracking
    initial_capital = Column(Float, nullable=False)
    current_capital = Column(Float, nullable=False)
    available_capital = Column(Float, nullable=False)
    
    # Performance metrics
    total_profit_loss = Column(Float, nullable=False, default=0.0)
    total_trades = Column(Integer, nullable=False, default=0)
    winning_trades = Column(Integer, nullable=False, default=0)
    losing_trades = Column(Integer, nullable=False, default=0)
    
    # Session timing
    start_time = Column(DateTime, nullable=False, default=datetime.now)
    end_time = Column(DateTime, nullable=True)
    
    # Configuration
    max_position_size = Column(Float, nullable=False, default=0.1)
    stop_loss_percent = Column(Float, nullable=False, default=0.15)
    take_profit_percent = Column(Float, nullable=False, default=0.25)
    
    # Status
    is_active = Column(Boolean, nullable=False, default=True)
    
    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

class PaperTradingPerformance(Base):
    """Database model for daily performance tracking"""
    __tablename__ = "paper_trading_performance"
    
    # Identification
    id = Column(String, primary_key=True)
    session_id = Column(String, nullable=False)
    date = Column(DateTime, nullable=False)
    
    # Daily metrics
    starting_capital = Column(Float, nullable=False)
    ending_capital = Column(Float, nullable=False)
    daily_pnl = Column(Float, nullable=False)
    daily_pnl_percent = Column(Float, nullable=False)
    
    # Trade statistics
    trades_opened = Column(Integer, nullable=False, default=0)
    trades_closed = Column(Integer, nullable=False, default=0)
    winning_trades = Column(Integer, nullable=False, default=0)
    losing_trades = Column(Integer, nullable=False, default=0)
    
    # Signal statistics
    signals_processed = Column(Integer, nullable=False, default=0)
    signals_traded = Column(Integer, nullable=False, default=0)
    average_confidence = Column(Float, nullable=False, default=0.0)
    
    # Risk metrics
    max_drawdown = Column(Float, nullable=False, default=0.0)
    max_position_value = Column(Float, nullable=False, default=0.0)
    
    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)
