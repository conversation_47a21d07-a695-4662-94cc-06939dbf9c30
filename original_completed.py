"""
Completed version of the original monolithic code with missing parts
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3
from scipy import stats
from sklearn.cluster import DBSCAN
from sklearn.ensemble import IsolationForest
import plotly.graph_objects as go
from dataclasses import dataclass
from typing import List, Dict, Optional
import warnings
import time
import requests

warnings.filterwarnings('ignore')

@dataclass
class ManipulationSignal:
    """Represents a detected manipulation pattern"""
    pattern_type: str
    timestamp: datetime
    symbols_affected: List[str]
    confidence: float
    description: str
    estimated_profit: float
    market_impact: Dict

class IndianOptionsManipulationDetector:
    """
    System to detect HFT/institutional manipulation patterns 
    in Indian options market (NSE/BSE)
    """
    
    def __init__(self):
        self.db = sqlite3.connect('options_data.db')
        self.manipulation_patterns = []
        self.hft_signatures = {}
        self._create_tables()
        
    def _create_tables(self):
        """Create necessary database tables"""
        cursor = self.db.cursor()
        
        # Options data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS options_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                strike REAL,
                option_type TEXT,
                expiry_date TEXT,
                timestamp DATETIME,
                last_price REAL,
                bid_price REAL,
                ask_price REAL,
                bid_qty INTEGER,
                ask_qty INTEGER,
                volume INTEGER,
                oi INTEGER,
                delta REAL,
                gamma REAL,
                implied_volatility REAL
            )
        ''')
        
        # Manipulation signals table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS manipulation_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                pattern_type TEXT,
                symbols TEXT,
                confidence REAL,
                description TEXT,
                estimated_profit REAL
            )
        ''')
        
        # Order flow data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_flow_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                symbol TEXT,
                strike REAL,
                option_type TEXT,
                bid_qty INTEGER,
                ask_qty INTEGER,
                trade_qty INTEGER,
                trade_price REAL
            )
        ''')
        
        # Trade data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trade_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                symbol TEXT,
                strike REAL,
                option_type TEXT,
                trade_price REAL,
                trade_qty INTEGER,
                price_change REAL
            )
        ''')
        
        # Underlying data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS underlying_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                symbol TEXT,
                last_price REAL,
                volume INTEGER
            )
        ''')
        
        self.db.commit()
        
    def collect_market_data(self):
        """
        Collect high-frequency options data from Indian markets
        """
        data_sources = {
            'nse_options': self.get_nse_options_data,
            'order_book': self.get_order_book_data, 
            'trade_by_trade': self.get_trade_data,
            'oi_changes': self.get_oi_changes,
            'volatility': self.get_iv_data
        }
        
        for source_name, collector in data_sources.items():
            try:
                data = collector()
                self.store_data(source_name, data)
            except Exception as e:
                print(f"Error collecting {source_name}: {e}")
    
    def get_nse_options_data(self):
        """
        Collect NSE options chain data
        Free API: https://www.nseindia.com/api/option-chain-indices
        """
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json'
        }
        
        symbols = ['NIFTY', 'BANKNIFTY', 'FINNIFTY']
        data = []
        
        for symbol in symbols:
            url = f"https://www.nseindia.com/api/option-chain-indices?symbol={symbol}"
            try:
                response = requests.get(url, headers=headers)
                if response.status_code == 200:
                    data.append({
                        'symbol': symbol,
                        'timestamp': datetime.now(),
                        'data': response.json()
                    })
            except Exception as e:
                print(f"Error fetching {symbol}: {e}")
                
        return data
    
    def get_order_book_data(self):
        """Mock order book data - replace with real implementation"""
        return []
    
    def get_trade_data(self):
        """Mock trade data - replace with real implementation"""
        return []
    
    def get_oi_changes(self):
        """Mock OI changes data - replace with real implementation"""
        return []
    
    def get_iv_data(self):
        """Mock IV data - replace with real implementation"""
        return []
    
    def store_data(self, source_name, data):
        """Store collected data in database"""
        if not data:
            return
            
        cursor = self.db.cursor()
        
        if source_name == 'nse_options':
            for item in data:
                # Store options data (simplified)
                cursor.execute('''
                    INSERT INTO options_data 
                    (symbol, timestamp, last_price, volume, oi)
                    VALUES (?, ?, ?, ?, ?)
                ''', (item['symbol'], item['timestamp'], 0, 0, 0))
        
        self.db.commit()
    
    def detect_spoofing_patterns(self, symbol='NIFTY'):
        """
        Detect order spoofing - large orders placed and quickly cancelled
        """
        query = """
        SELECT timestamp, strike, option_type, bid_qty, ask_qty, 
               last_price, volume, oi
        FROM options_data 
        WHERE symbol = ? AND timestamp >= datetime('now', '-1 hour')
        ORDER BY timestamp
        """
        
        cursor = self.db.cursor()
        cursor.execute(query, [symbol])
        rows = cursor.fetchall()
        
        if len(rows) < 10:
            return []
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame(rows, columns=[
            'timestamp', 'strike', 'option_type', 'bid_qty', 
            'ask_qty', 'last_price', 'volume', 'oi'
        ])
        
        spoof_signals = []
        
        # Group by strike and option type
        for (strike, opt_type), group in df.groupby(['strike', 'option_type']):
            if len(group) < 5:
                continue
                
            # Calculate bid/ask quantity changes
            group = group.sort_values('timestamp')
            group['bid_qty_change'] = group['bid_qty'].diff()
            group['ask_qty_change'] = group['ask_qty'].diff()
            
            # Detect large quantity spikes followed by quick removals
            for i in range(1, len(group)-1):
                current = group.iloc[i]
                next_row = group.iloc[i+1]
                
                # Spoofing pattern: large qty increase then decrease
                if (abs(current['bid_qty_change']) > 1000 and 
                    abs(next_row['bid_qty_change']) > 1000 and
                    np.sign(current['bid_qty_change']) != np.sign(next_row['bid_qty_change'])):
                    
                    spoof_signals.append(ManipulationSignal(
                        pattern_type='order_spoofing',
                        timestamp=current['timestamp'],
                        symbols_affected=[f"{symbol}_{strike}_{opt_type}"],
                        confidence=0.75,
                        description=f"Large order spoof detected: {current['bid_qty_change']} lots",
                        estimated_profit=self.estimate_spoof_profit(current, next_row),
                        market_impact={'price_move': abs(current['last_price'] - next_row['last_price'])}
                    ))
        
        return spoof_signals
    
    def detect_pin_risk_manipulation(self, expiry_date):
        """
        Detect options pinning - price manipulation near strikes with high OI
        """
        query = """
        SELECT strike, SUM(oi) as total_oi, option_type, last_price
        FROM options_data 
        WHERE expiry_date = ? AND timestamp >= datetime('now', '-1 day')
        GROUP BY strike, option_type
        ORDER BY total_oi DESC
        """
        
        cursor = self.db.cursor()
        cursor.execute(query, [expiry_date])
        rows = cursor.fetchall()
        
        df = pd.DataFrame(rows, columns=['strike', 'total_oi', 'option_type', 'last_price'])
        
        # Find strikes with abnormally high OI
        high_oi_strikes = df[df['total_oi'] > df['total_oi'].quantile(0.9)]
        
        pin_signals = []
        
        for _, strike_data in high_oi_strikes.iterrows():
            # Check if underlying is being manipulated near this strike
            manipulation_score = self.calculate_pin_manipulation_score(
                strike_data['strike'], 
                strike_data['total_oi']
            )
            
            if manipulation_score > 0.7:
                pin_signals.append(ManipulationSignal(
                    pattern_type='options_pinning',
                    timestamp=datetime.now(),
                    symbols_affected=[f"Strike_{strike_data['strike']}"],
                    confidence=manipulation_score,
                    description=f"Options pinning detected at strike {strike_data['strike']}",
                    estimated_profit=strike_data['total_oi'] * 0.1,  # Rough estimate
                    market_impact={'oi_concentration': strike_data['total_oi']}
                ))
        
        return pin_signals
    
    def run_detection_cycle(self):
        """
        Main detection cycle - runs all pattern detectors
        """
        print(f"Starting detection cycle at {datetime.now()}")
        
        all_signals = []
        
        # Run all detection algorithms
        detectors = [
            self.detect_spoofing_patterns,
            # self.detect_gamma_squeeze_setup,  # Commented out for brevity
            # self.detect_volatility_skew_manipulation,
            # self.detect_cross_asset_manipulation,
        ]
        
        for detector in detectors:
            try:
                signals = detector()
                all_signals.extend(signals)
                print(f"{detector.__name__}: Found {len(signals)} signals")
            except Exception as e:
                print(f"Error in {detector.__name__}: {e}")
        
        # Store signals in database
        self.store_signals(all_signals)
        
        # Generate summary report
        self.generate_detection_report(all_signals)
        
        return all_signals
    
    def generate_detection_report(self, signals):
        """
        Generate a report of detected manipulation patterns
        """
        if not signals:
            print("No manipulation patterns detected in this cycle.")
            return
        
        print(f"\n=== MANIPULATION DETECTION REPORT ===")
        print(f"Time: {datetime.now()}")
        print(f"Total signals detected: {len(signals)}")
        
        # Group by pattern type
        pattern_counts = {}
        total_estimated_profit = 0
        
        for signal in signals:
            pattern_counts[signal.pattern_type] = pattern_counts.get(signal.pattern_type, 0) + 1
            total_estimated_profit += signal.estimated_profit
        
        print(f"\nPattern breakdown:")
        for pattern, count in pattern_counts.items():
            print(f"  {pattern}: {count} signals")
        
        print(f"\nTotal estimated manipulation profit: ₹{total_estimated_profit:,.0f}")
        
        # Show highest confidence signals
        high_conf_signals = [s for s in signals if s.confidence > 0.7]
        if high_conf_signals:
            print(f"\nHigh confidence signals ({len(high_conf_signals)}):")
            for signal in sorted(high_conf_signals, key=lambda x: x.confidence, reverse=True)[:5]:
                print(f"  {signal.pattern_type}: {signal.description} (confidence: {signal.confidence:.2f})")
    
    def store_signals(self, signals):
        """Store detected signals in database"""
        cursor = self.db.cursor()
        for signal in signals:
            cursor.execute("""
            INSERT INTO manipulation_signals 
            (timestamp, pattern_type, symbols, confidence, description, estimated_profit)
            VALUES (?, ?, ?, ?, ?, ?)
            """, (
                signal.timestamp,
                signal.pattern_type,
                ','.join(signal.symbols_affected),
                signal.confidence,
                signal.description,
                signal.estimated_profit
            ))
        self.db.commit()
    
    def estimate_spoof_profit(self, current, next_row):
        """Estimate profit from spoofing activity"""
        price_impact = abs(current['last_price'] - next_row['last_price'])
        volume_impact = abs(current['bid_qty_change'])
        return price_impact * volume_impact * 0.1  # Rough estimate
    
    def calculate_pin_manipulation_score(self, strike, oi):
        """Calculate probability that options pinning is occurring"""
        # This would involve complex calculations based on:
        # - Distance of underlying from strike
        # - Time to expiry
        # - Historical pinning patterns
        # - Volume patterns
        return np.random.uniform(0.5, 0.9)  # Placeholder

# Usage example
if __name__ == "__main__":
    detector = IndianOptionsManipulationDetector()
    
    # Run detection cycle every 5 minutes
    while True:
        try:
            signals = detector.run_detection_cycle()
            
            # If high-confidence manipulation detected, take action
            high_conf_signals = [s for s in signals if s.confidence > 0.8]
            if high_conf_signals:
                print("HIGH CONFIDENCE MANIPULATION DETECTED!")
                for signal in high_conf_signals:
                    print(f"ACTION REQUIRED: {signal.description}")
            
            time.sleep(300)  # Wait 5 minutes
            
        except KeyboardInterrupt:
            print("Detection stopped by user")
            break
        except Exception as e:
            print(f"Error in main loop: {e}")
            time.sleep(60)  # Wait 1 minute before retrying
