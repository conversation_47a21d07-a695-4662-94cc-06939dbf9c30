"""
Gamma Squeeze Detection Algorithm
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any
import logging

from detection.base_detector import BaseDetector
from models.data_models import ManipulationSignal, OptionsData, PatternType
from config.settings import settings

logger = logging.getLogger(__name__)

class GammaSqueezeDetector(BaseDetector):
    """
    Detects gamma squeeze setups - when dealers need to hedge aggressively
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("gamma_squeeze_detector", config)
        
        # Configuration parameters
        self.gamma_exposure_threshold = self.config.get(
            "gamma_exposure_threshold",
            settings.detection.gamma_exposure_threshold
        )
        self.volume_ratio_threshold = self.config.get(
            "volume_ratio_threshold",
            settings.detection.gamma_volume_ratio
        )
        self.min_oi_threshold = self.config.get("min_oi_threshold", 1000)
        self.confidence_base = self.config.get("confidence_base", 0.7)
        
    def get_required_data_window(self) -> int:
        """Minimum data points needed for gamma squeeze detection"""
        return 30  # Need sufficient data to calculate gamma exposure
    
    async def detect(self, data: List[OptionsData]) -> List[ManipulationSignal]:
        """
        Detect gamma squeeze patterns
        
        Args:
            data: List of options data to analyze
            
        Returns:
            List of detected gamma squeeze signals
        """
        signals = []
        
        try:
            # Convert to DataFrame for easier analysis
            df = self._prepare_dataframe(data)
            
            if df.empty:
                return signals
            
            # Calculate gamma exposure by symbol and strike
            gamma_exposure_df = self._calculate_gamma_exposure(df)
            
            # Detect gamma squeeze setups
            for _, row in gamma_exposure_df.iterrows():
                if self._is_gamma_squeeze_setup(row, df):
                    signal = self._create_gamma_squeeze_signal(row, df)
                    signals.append(signal)
            
            logger.info(f"Gamma squeeze detector found {len(signals)} potential signals")
            
        except Exception as e:
            logger.error(f"Error in gamma squeeze detection: {str(e)}")
            raise
        
        return signals
    
    def _prepare_dataframe(self, data: List[OptionsData]) -> pd.DataFrame:
        """
        Convert options data to DataFrame for analysis
        
        Args:
            data: List of options data
            
        Returns:
            Prepared DataFrame
        """
        records = []
        for option in data:
            # Only include options with gamma data
            if option.gamma is not None and option.gamma > 0:
                records.append({
                    'symbol': option.symbol,
                    'strike': option.strike,
                    'option_type': option.option_type.value,
                    'timestamp': option.timestamp,
                    'last_price': option.last_price,
                    'volume': option.volume,
                    'open_interest': option.open_interest,
                    'delta': option.delta or 0,
                    'gamma': option.gamma,
                    'implied_volatility': option.implied_volatility or 0
                })
        
        df = pd.DataFrame(records)
        
        if not df.empty:
            # Ensure timestamp is datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Calculate additional metrics
            df['gamma_dollar'] = df['gamma'] * df['last_price'] * 100  # 100 shares per lot
            df['total_gamma_exposure'] = df['gamma_dollar'] * df['open_interest']
            
        return df
    
    def _calculate_gamma_exposure(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate gamma exposure by symbol and strike
        
        Args:
            df: Options DataFrame
            
        Returns:
            DataFrame with gamma exposure calculations
        """
        # Group by symbol and strike
        gamma_exposure = df.groupby(['symbol', 'strike']).agg({
            'gamma': 'sum',
            'open_interest': 'sum',
            'volume': 'sum',
            'total_gamma_exposure': 'sum',
            'last_price': 'mean',
            'implied_volatility': 'mean',
            'timestamp': 'max'
        }).reset_index()
        
        # Calculate net gamma exposure (calls positive, puts negative)
        call_gamma = df[df['option_type'] == 'CE'].groupby(['symbol', 'strike'])['total_gamma_exposure'].sum()
        put_gamma = df[df['option_type'] == 'PE'].groupby(['symbol', 'strike'])['total_gamma_exposure'].sum()
        
        gamma_exposure['call_gamma_exposure'] = gamma_exposure.apply(
            lambda row: call_gamma.get((row['symbol'], row['strike']), 0), axis=1
        )
        gamma_exposure['put_gamma_exposure'] = gamma_exposure.apply(
            lambda row: put_gamma.get((row['symbol'], row['strike']), 0), axis=1
        )
        
        # Net gamma exposure (market makers are short gamma when positive)
        gamma_exposure['net_gamma_exposure'] = (
            gamma_exposure['call_gamma_exposure'] - gamma_exposure['put_gamma_exposure']
        )
        
        # Sort by absolute gamma exposure
        gamma_exposure['abs_gamma_exposure'] = gamma_exposure['net_gamma_exposure'].abs()
        gamma_exposure = gamma_exposure.sort_values('abs_gamma_exposure', ascending=False)
        
        return gamma_exposure
    
    def _is_gamma_squeeze_setup(self, gamma_row: pd.Series, df: pd.DataFrame) -> bool:
        """
        Check if a gamma squeeze setup exists
        
        Args:
            gamma_row: Row from gamma exposure DataFrame
            df: Original options DataFrame
            
        Returns:
            True if gamma squeeze setup detected
        """
        # Check gamma exposure threshold
        if gamma_row['abs_gamma_exposure'] < self.gamma_exposure_threshold:
            return False
        
        # Check minimum open interest
        if gamma_row['open_interest'] < self.min_oi_threshold:
            return False
        
        # Check recent volume activity
        symbol = gamma_row['symbol']
        strike = gamma_row['strike']
        
        # Get recent volume for this strike
        recent_data = df[
            (df['symbol'] == symbol) & 
            (df['strike'] == strike) &
            (df['timestamp'] >= gamma_row['timestamp'] - timedelta(hours=2))
        ]
        
        if recent_data.empty:
            return False
        
        recent_volume = recent_data['volume'].sum()
        total_volume = gamma_row['volume']
        
        # Check if significant portion of volume is recent
        if total_volume > 0:
            volume_ratio = recent_volume / total_volume
            if volume_ratio < self.volume_ratio_threshold:
                return False
        
        # Check if we're near the strike (gamma is highest ATM)
        underlying_price = self._estimate_underlying_price(df, symbol)
        if underlying_price:
            strike_distance = abs(gamma_row['strike'] - underlying_price) / underlying_price
            # Only consider strikes within 5% of underlying
            if strike_distance > 0.05:
                return False
        
        return True
    
    def _estimate_underlying_price(self, df: pd.DataFrame, symbol: str) -> float:
        """
        Estimate underlying price from options data
        
        Args:
            df: Options DataFrame
            symbol: Symbol to estimate price for
            
        Returns:
            Estimated underlying price
        """
        symbol_data = df[df['symbol'] == symbol]
        
        if symbol_data.empty:
            return 0
        
        # Use ATM call option price as proxy
        # Find strike closest to current price estimate
        strikes = symbol_data['strike'].unique()
        
        # Simple estimation: use middle strike as approximation
        if len(strikes) > 0:
            return np.median(strikes)
        
        return 0
    
    def _create_gamma_squeeze_signal(
        self, 
        gamma_row: pd.Series, 
        df: pd.DataFrame
    ) -> ManipulationSignal:
        """
        Create a manipulation signal for detected gamma squeeze
        
        Args:
            gamma_row: Gamma exposure data
            df: Original options DataFrame
            
        Returns:
            ManipulationSignal instance
        """
        symbol = gamma_row['symbol']
        strike = gamma_row['strike']
        
        # Calculate confidence
        confidence = self._calculate_confidence(gamma_row, df)
        
        # Estimate potential profit
        estimated_profit = self._estimate_profit(gamma_row)
        
        # Create description
        description = self._create_description(gamma_row)
        
        return ManipulationSignal(
            pattern_type=PatternType.GAMMA_SQUEEZE_SETUP,
            timestamp=gamma_row['timestamp'],
            symbols_affected=[f"{symbol}_{strike}"],
            confidence=confidence,
            description=description,
            estimated_profit=estimated_profit,
            market_impact={
                "gamma_exposure": gamma_row['net_gamma_exposure'],
                "open_interest": gamma_row['open_interest'],
                "recent_volume": gamma_row['volume'],
                "strike": strike,
                "underlying_estimate": self._estimate_underlying_price(df, symbol)
            }
        )
    
    def _calculate_confidence(self, gamma_row: pd.Series, df: pd.DataFrame) -> float:
        """
        Calculate confidence score for gamma squeeze detection
        
        Args:
            gamma_row: Gamma exposure data
            df: Original options DataFrame
            
        Returns:
            Confidence score between 0 and 1
        """
        confidence = self.confidence_base
        
        # Increase confidence based on gamma exposure size
        gamma_exposure = gamma_row['abs_gamma_exposure']
        if gamma_exposure > self.gamma_exposure_threshold * 2:
            confidence += 0.1
        if gamma_exposure > self.gamma_exposure_threshold * 5:
            confidence += 0.1
        
        # Increase confidence based on open interest concentration
        oi = gamma_row['open_interest']
        if oi > self.min_oi_threshold * 2:
            confidence += 0.05
        if oi > self.min_oi_threshold * 5:
            confidence += 0.05
        
        # Increase confidence if strike is close to underlying
        symbol = gamma_row['symbol']
        underlying_price = self._estimate_underlying_price(df, symbol)
        if underlying_price:
            strike_distance = abs(gamma_row['strike'] - underlying_price) / underlying_price
            if strike_distance < 0.02:  # Within 2%
                confidence += 0.1
            elif strike_distance < 0.01:  # Within 1%
                confidence += 0.15
        
        return min(confidence, 0.95)  # Cap at 95%
    
    def _estimate_profit(self, gamma_row: pd.Series) -> float:
        """
        Estimate potential profit from gamma squeeze
        
        Args:
            gamma_row: Gamma exposure data
            
        Returns:
            Estimated profit in rupees
        """
        # Rough estimation based on gamma exposure and potential price movement
        gamma_exposure = gamma_row['abs_gamma_exposure']
        
        # Assume 1% price movement could generate significant hedging flow
        potential_move = 0.01
        estimated_profit = gamma_exposure * potential_move
        
        return max(estimated_profit, 0)
    
    def _create_description(self, gamma_row: pd.Series) -> str:
        """
        Create human-readable description of gamma squeeze
        
        Args:
            gamma_row: Gamma exposure data
            
        Returns:
            Description string
        """
        symbol = gamma_row['symbol']
        strike = gamma_row['strike']
        gamma_exposure = gamma_row['net_gamma_exposure']
        oi = gamma_row['open_interest']
        
        direction = "positive" if gamma_exposure > 0 else "negative"
        
        return (
            f"Gamma squeeze setup detected: {symbol} {strike} strike with "
            f"{direction} gamma exposure of ₹{abs(gamma_exposure):,.0f} "
            f"and {oi:,.0f} OI concentration"
        )
