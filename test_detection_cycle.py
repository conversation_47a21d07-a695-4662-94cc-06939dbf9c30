#!/usr/bin/env python3
"""
Test a single detection cycle
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# Set UTF-8 encoding for Windows console
if sys.platform == "win32":
    os.environ["PYTHONIOENCODING"] = "utf-8"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

async def test_detection_cycle():
    """Test a single detection cycle"""
    try:
        print("🔍 Testing Single Detection Cycle")
        print("=" * 50)
        
        # Import components
        from core.detection_engine import detection_engine
        from config.settings import settings
        
        print(f"📊 Symbols to monitor: {settings.symbols}")
        print(f"🔧 Detection interval: {settings.detection_interval} seconds")
        
        # Initialize detection engine
        print("🚀 Initializing detection engine...")
        await detection_engine.initialize()
        print("✅ Detection engine initialized")
        
        # Run a single detection cycle
        print("🔄 Running single detection cycle...")
        signals = await detection_engine.run_detection_cycle()
        
        print(f"📈 Detection cycle completed!")
        print(f"🎯 Signals detected: {len(signals)}")
        
        if signals:
            print("\n📋 Detected Signals:")
            for i, signal in enumerate(signals, 1):
                print(f"   {i}. {signal.pattern_type.value}")
                print(f"      Confidence: {signal.confidence:.1%}")
                print(f"      Description: {signal.description}")
                print(f"      Estimated Profit: Rs{signal.estimated_profit:,.0f}")
                print()
        else:
            print("ℹ️  No manipulation signals detected in this cycle")
        
        # Get statistics
        stats = detection_engine.get_statistics()
        print("\n📊 Engine Statistics:")
        print(f"   Cycles completed: {stats['cycles_completed']}")
        print(f"   Total signals: {stats['total_signals']}")
        print(f"   High confidence signals: {stats['high_confidence_signals']}")
        print(f"   Detector count: {stats['detector_count']}")
        
        # Shutdown
        print("\n🔄 Shutting down...")
        await detection_engine.shutdown()
        print("✅ Shutdown complete")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in detection cycle test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    success = await test_detection_cycle()
    if success:
        print("\n🎉 Detection cycle test completed successfully!")
    else:
        print("\n❌ Detection cycle test failed!")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        sys.exit(1)
