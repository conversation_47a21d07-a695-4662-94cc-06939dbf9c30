"""
Test the fixed scalable system
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_detection_engine():
    """Test the detection engine without conflicts"""
    print("🔍 Testing Fixed Detection Engine...")
    
    try:
        from core.detection_engine import detection_engine
        from models.data_models import OptionsData, OptionType
        
        # Initialize the engine
        await detection_engine.initialize()
        print("✅ Detection engine initialized successfully")
        
        # Create sample data
        options_data = []
        base_time = datetime.now()
        
        for i in range(25):
            option = OptionsData(
                symbol="NIFTY",
                expiry_date=datetime(2024, 1, 25),
                strike=21000.0,
                option_type=OptionType.CALL,
                last_price=150.0 + (i * 0.1),
                bid_price=149.0 + (i * 0.1),
                ask_price=151.0 + (i * 0.1),
                volume=100 + i,
                open_interest=5000,
                bid_qty=100 if i != 10 else 2000,  # Spike at position 10
                ask_qty=200,
                timestamp=base_time + timedelta(seconds=i * 30)
            )
            options_data.append(option)
        
        print(f"   Created {len(options_data)} sample data points")
        
        # Test detection without running full cycle
        from detection.base_detector import detector_registry
        
        if detector_registry.list_detectors():
            results = await detector_registry.run_all_detectors(options_data)
            
            total_signals = sum(len(result.signals) for result in results)
            print(f"✅ Detection completed successfully")
            print(f"   Detectors run: {len(results)}")
            print(f"   Total signals: {total_signals}")
            
            for result in results:
                print(f"   {result.algorithm_name}: {result.signals_found} signals in {result.execution_time:.3f}s")
                if result.errors:
                    print(f"     Errors: {result.errors}")
        
        # Cleanup
        await detection_engine.shutdown()
        print("✅ Detection engine shutdown successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Detection engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_server():
    """Test API server initialization"""
    print("\n🔍 Testing Fixed API Server...")
    
    try:
        from api.main import app
        print("✅ API module imports successfully")
        
        # Check routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                methods = list(route.methods) if hasattr(route.methods, '__iter__') else [str(route.methods)]
                routes.append(f"{methods} {route.path}")
        
        print(f"📡 Found {len(routes)} API endpoints:")
        for route in routes[:5]:  # Show first 5
            print(f"   {route}")
        
        return True
        
    except Exception as e:
        print(f"❌ API server test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_nse_collector():
    """Test NSE data collector"""
    print("\n🔍 Testing NSE Data Collector...")
    
    try:
        from data_collectors.nse_collector import NSEDataCollector
        
        async with NSEDataCollector() as collector:
            print("✅ NSE collector initialized")
            
            # Test statistics
            stats = collector.get_statistics()
            print(f"   Collector stats: {stats}")
            
            # Test market status (lightweight)
            try:
                market_status = await collector.get_market_status()
                print(f"✅ Market status retrieved: {market_status.get('timestamp')}")
            except Exception as e:
                print(f"⚠️  Market status failed (normal if NSE blocks): {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ NSE collector test failed: {e}")
        return False

async def test_database_operations():
    """Test database operations"""
    print("\n🔍 Testing Database Operations...")
    
    try:
        from utils.database import db_manager
        from models.data_models import ManipulationSignal, PatternType
        
        # Initialize database
        await db_manager.initialize()
        print("✅ Database initialized")
        
        # Test storing a signal
        test_signal = ManipulationSignal(
            pattern_type=PatternType.ORDER_SPOOFING,
            timestamp=datetime.now(),
            symbols_affected=["TEST_21000_CE"],
            confidence=0.85,
            description="Test signal for system verification",
            estimated_profit=10000.0,
            market_impact={"test": True}
        )
        
        await db_manager.store_manipulation_signals([test_signal])
        print("✅ Signal stored successfully")
        
        # Test retrieving signals
        signals = await db_manager.get_manipulation_signals(hours=1, min_confidence=0.0)
        print(f"✅ Retrieved {len(signals)} signals from database")
        
        # Cleanup
        await db_manager.close()
        print("✅ Database connection closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all fixed system tests"""
    print("🚀 Testing Fixed Options Manipulation Detection System")
    print("=" * 70)
    
    tests = [
        ("API Server", test_api_server),
        ("NSE Data Collector", test_nse_collector),
        ("Database Operations", test_database_operations),
        ("Detection Engine", test_detection_engine),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 Fixed System Test Results:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scalable system is now working!")
        print("\n🚀 Ready to Use:")
        print("   1. Full system: python main.py --mode full")
        print("   2. Detection only: python main.py --mode detection")
        print("   3. API only: python main.py --mode api")
        print("   4. Docker: docker-compose up -d")
        
        print("\n📡 API Endpoints (when running):")
        print("   • http://localhost:8080/health - Health check")
        print("   • http://localhost:8080/signals - Get manipulation signals")
        print("   • http://localhost:8080/statistics - System statistics")
        print("   • http://localhost:8080/docs - API documentation")
        
        return True
    elif passed >= total * 0.75:
        print("✅ Most components working! System is mostly functional.")
        return True
    else:
        print("❌ System still has issues. Check error messages above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
