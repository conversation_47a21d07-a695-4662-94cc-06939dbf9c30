"""
Basic functionality tests for the Options Manipulation Detection System
"""
import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from models.data_models import OptionsData, OptionType, PatternType, ManipulationSignal
from detection.spoofing_detector import SpoofingDetector
from detection.gamma_squeeze_detector import GammaSqueezeDetector
from data_collectors.nse_collector import NSEDataCollector
from config.settings import settings

class TestDataModels:
    """Test data models and validation"""
    
    def test_options_data_creation(self):
        """Test OptionsData model creation"""
        option = OptionsData(
            symbol="NIFTY",
            expiry_date=datetime(2024, 1, 25),
            strike=21000.0,
            option_type=OptionType.CALL,
            last_price=150.0,
            bid_price=149.0,
            ask_price=151.0,
            volume=1000,
            open_interest=5000,
            bid_qty=100,
            ask_qty=200,
            timestamp=datetime.now()
        )
        
        assert option.symbol == "NIFTY"
        assert option.option_type == OptionType.CALL
        assert option.strike == 21000.0
        assert option.volume == 1000
    
    def test_manipulation_signal_creation(self):
        """Test ManipulationSignal creation"""
        signal = ManipulationSignal(
            pattern_type=PatternType.ORDER_SPOOFING,
            timestamp=datetime.now(),
            symbols_affected=["NIFTY_21000_CE"],
            confidence=0.85,
            description="Test spoofing signal",
            estimated_profit=50000.0,
            market_impact={"price_move": 2.5}
        )
        
        assert signal.pattern_type == PatternType.ORDER_SPOOFING
        assert signal.confidence == 0.85
        assert signal.confidence_level.value == "high"
        assert len(signal.symbols_affected) == 1

class TestSpoofingDetector:
    """Test spoofing detection algorithm"""
    
    @pytest.fixture
    def spoofing_detector(self):
        """Create spoofing detector instance"""
        return SpoofingDetector()
    
    @pytest.fixture
    def sample_options_data(self):
        """Create sample options data for testing"""
        base_time = datetime.now()
        data = []
        
        # Create a sequence of options data showing spoofing pattern
        for i in range(10):
            option = OptionsData(
                symbol="NIFTY",
                expiry_date=datetime(2024, 1, 25),
                strike=21000.0,
                option_type=OptionType.CALL,
                last_price=150.0 + (i * 0.5),  # Gradual price increase
                bid_price=149.0 + (i * 0.5),
                ask_price=151.0 + (i * 0.5),
                volume=100 * (i + 1),
                open_interest=5000,
                bid_qty=100 if i != 5 else 2000,  # Spike at position 5
                ask_qty=200,
                timestamp=base_time + timedelta(seconds=i * 30)
            )
            data.append(option)
        
        return data
    
    def test_detector_initialization(self, spoofing_detector):
        """Test detector initialization"""
        assert spoofing_detector.name == "spoofing_detector"
        assert spoofing_detector.enabled == True
        assert spoofing_detector.qty_threshold == settings.detection.spoof_qty_threshold
    
    @pytest.mark.asyncio
    async def test_spoofing_detection(self, spoofing_detector, sample_options_data):
        """Test spoofing detection with sample data"""
        signals = await spoofing_detector.detect(sample_options_data)
        
        # Should detect some patterns in the sample data
        assert isinstance(signals, list)
        # Note: Actual detection depends on the specific pattern in sample data
    
    def test_required_data_window(self, spoofing_detector):
        """Test required data window"""
        assert spoofing_detector.get_required_data_window() == 20

class TestGammaSqueezeDetector:
    """Test gamma squeeze detection algorithm"""
    
    @pytest.fixture
    def gamma_detector(self):
        """Create gamma squeeze detector instance"""
        return GammaSqueezeDetector()
    
    @pytest.fixture
    def sample_gamma_data(self):
        """Create sample data with gamma information"""
        base_time = datetime.now()
        data = []
        
        # Create options with gamma data
        strikes = [20800, 20900, 21000, 21100, 21200]
        
        for strike in strikes:
            # Call option
            call_option = OptionsData(
                symbol="NIFTY",
                expiry_date=datetime(2024, 1, 25),
                strike=float(strike),
                option_type=OptionType.CALL,
                last_price=100.0,
                bid_price=99.0,
                ask_price=101.0,
                volume=500,
                open_interest=2000,
                bid_qty=100,
                ask_qty=150,
                gamma=0.05 if strike == 21000 else 0.02,  # High gamma at ATM
                delta=0.5 if strike == 21000 else 0.3,
                timestamp=base_time
            )
            data.append(call_option)
            
            # Put option
            put_option = OptionsData(
                symbol="NIFTY",
                expiry_date=datetime(2024, 1, 25),
                strike=float(strike),
                option_type=OptionType.PUT,
                last_price=80.0,
                bid_price=79.0,
                ask_price=81.0,
                volume=300,
                open_interest=1500,
                bid_qty=80,
                ask_qty=120,
                gamma=0.05 if strike == 21000 else 0.02,
                delta=-0.5 if strike == 21000 else -0.3,
                timestamp=base_time
            )
            data.append(put_option)
        
        return data
    
    def test_gamma_detector_initialization(self, gamma_detector):
        """Test gamma detector initialization"""
        assert gamma_detector.name == "gamma_squeeze_detector"
        assert gamma_detector.enabled == True
    
    @pytest.mark.asyncio
    async def test_gamma_detection(self, gamma_detector, sample_gamma_data):
        """Test gamma squeeze detection"""
        signals = await gamma_detector.detect(sample_gamma_data)
        
        assert isinstance(signals, list)
        # Gamma detection might not trigger with small sample data

class TestNSEDataCollector:
    """Test NSE data collection"""
    
    @pytest.fixture
    def mock_response_data(self):
        """Mock NSE API response data"""
        return {
            "records": {
                "data": [
                    {
                        "strikePrice": 21000,
                        "expiryDate": "25-Jan-2024",
                        "CE": {
                            "lastPrice": 150.0,
                            "bidprice": 149.0,
                            "askPrice": 151.0,
                            "totalTradedVolume": 1000,
                            "openInterest": 5000,
                            "bidQty": 100,
                            "askQty": 200,
                            "change": 2.5,
                            "pChange": 1.69
                        },
                        "PE": {
                            "lastPrice": 80.0,
                            "bidprice": 79.0,
                            "askPrice": 81.0,
                            "totalTradedVolume": 800,
                            "openInterest": 4000,
                            "bidQty": 150,
                            "askQty": 180,
                            "change": -1.5,
                            "pChange": -1.83
                        }
                    }
                ]
            }
        }
    
    @pytest.mark.asyncio
    async def test_nse_collector_initialization(self):
        """Test NSE collector initialization"""
        async with NSEDataCollector() as collector:
            assert collector.session is not None
            assert collector.request_count == 0
            assert collector.error_count == 0
    
    @pytest.mark.asyncio
    async def test_options_chain_parsing(self, mock_response_data):
        """Test options chain data parsing"""
        collector = NSEDataCollector()
        await collector.start_session()
        
        try:
            # Mock the API request
            with patch.object(collector, '_make_request', new_callable=AsyncMock) as mock_request:
                mock_request.return_value = mock_response_data
                
                options_data = await collector.get_options_chain("NIFTY")
                
                assert len(options_data) == 2  # One call, one put
                assert options_data[0].symbol == "NIFTY"
                assert options_data[0].strike == 21000.0
                assert options_data[0].option_type in [OptionType.CALL, OptionType.PUT]
                
        finally:
            await collector.close_session()

class TestConfiguration:
    """Test configuration management"""
    
    def test_settings_loading(self):
        """Test settings are loaded correctly"""
        assert settings.symbols == ["NIFTY", "BANKNIFTY", "FINNIFTY"]
        assert settings.detection.spoof_qty_threshold == 1000
        assert settings.detection.high_confidence_threshold == 0.8
        assert settings.api.port == 8080
    
    def test_database_settings(self):
        """Test database configuration"""
        assert settings.database.pool_size == 20
        assert settings.database.max_overflow == 30
        assert "postgresql://" in settings.database.url
    
    def test_nse_api_settings(self):
        """Test NSE API configuration"""
        assert settings.nse_api.requests_per_minute == 60
        assert settings.nse_api.requests_per_second == 2
        assert "User-Agent" in settings.nse_api.headers

@pytest.mark.asyncio
async def test_end_to_end_detection():
    """Test end-to-end detection flow"""
    # Create sample data
    base_time = datetime.now()
    options_data = []
    
    for i in range(25):  # Enough data for detection
        option = OptionsData(
            symbol="NIFTY",
            expiry_date=datetime(2024, 1, 25),
            strike=21000.0,
            option_type=OptionType.CALL,
            last_price=150.0,
            bid_price=149.0,
            ask_price=151.0,
            volume=100,
            open_interest=5000,
            bid_qty=100,
            ask_qty=200,
            timestamp=base_time + timedelta(seconds=i * 30)
        )
        options_data.append(option)
    
    # Test spoofing detector
    spoofing_detector = SpoofingDetector()
    spoofing_signals = await spoofing_detector.detect(options_data)
    
    assert isinstance(spoofing_signals, list)
    
    # Test gamma detector
    gamma_detector = GammaSqueezeDetector()
    gamma_signals = await gamma_detector.detect(options_data)
    
    assert isinstance(gamma_signals, list)

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
