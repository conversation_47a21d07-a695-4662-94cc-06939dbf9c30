#!/usr/bin/env python3
"""
Comprehensive demonstration of the Options Manipulation Detection System
"""
import asyncio
import requests
import json
import time
from datetime import datetime

def print_banner():
    """Print system banner"""
    print("=" * 80)
    print("🚀 OPTIONS MANIPULATION DETECTION SYSTEM - LIVE DEMONSTRATION")
    print("=" * 80)
    print("📊 Real-time detection of manipulation patterns in Indian options markets")
    print("🔍 Monitoring: NSE/BSE Options (NIFTY, BANKNIFTY, FINNIFTY)")
    print("⚡ Technology: Python 3.11, FastAPI, SQLAlchemy, Redis, Prometheus")
    print("=" * 80)
    print()

def test_api_endpoints():
    """Test all API endpoints"""
    base_url = "http://localhost:8081"
    
    print("🌐 TESTING API ENDPOINTS")
    print("-" * 40)
    
    endpoints = [
        ("/", "Root endpoint"),
        ("/health", "Health check"),
        ("/statistics", "System statistics"),
        ("/detectors", "Detection algorithms"),
        ("/signals", "Manipulation signals"),
        ("/signals/summary", "Signals summary")
    ]
    
    for endpoint, description in endpoints:
        try:
            print(f"📡 Testing {endpoint} - {description}")
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ SUCCESS - {data.get('message', 'OK')}")
                
                # Show key data for interesting endpoints
                if endpoint == "/statistics":
                    stats = data.get('data', {})
                    engine_stats = stats.get('detection_engine', {})
                    system_stats = stats.get('system_metrics', {})
                    print(f"      🔧 Detectors: {engine_stats.get('detector_count', 0)}")
                    print(f"      💾 Memory: {system_stats.get('memory_usage_mb', 0):.1f} MB")
                    print(f"      ⏱️  Uptime: {system_stats.get('uptime_seconds', 0):.1f}s")
                
                elif endpoint == "/detectors":
                    detectors = data.get('data', {}).get('detectors', [])
                    for detector in detectors:
                        print(f"      🔍 {detector['name']}: {'Enabled' if detector['enabled'] else 'Disabled'}")
                
                elif endpoint == "/signals":
                    signals = data.get('data', {}).get('signals', [])
                    print(f"      📈 Signals found: {len(signals)}")
                    
            else:
                print(f"   ❌ FAILED - HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR - {str(e)}")
        
        print()

def run_detection_test():
    """Run a detection cycle test"""
    print("🔍 RUNNING DETECTION CYCLE TEST")
    print("-" * 40)
    
    try:
        # Trigger a detection cycle
        response = requests.post("http://localhost:8081/detection/run", timeout=10)
        
        if response.status_code == 200:
            print("✅ Detection cycle triggered successfully")
            
            # Wait a moment for the cycle to complete
            print("⏳ Waiting for detection cycle to complete...")
            time.sleep(3)
            
            # Check for new signals
            signals_response = requests.get("http://localhost:8081/signals", timeout=5)
            if signals_response.status_code == 200:
                signals_data = signals_response.json()
                signals = signals_data.get('data', {}).get('signals', [])
                
                print(f"📊 Detection cycle completed")
                print(f"🎯 Signals detected: {len(signals)}")
                
                if signals:
                    print("\n📋 Detected Manipulation Signals:")
                    for i, signal in enumerate(signals, 1):
                        print(f"   {i}. Pattern: {signal['pattern_type']}")
                        print(f"      Confidence: {signal['confidence']:.1%}")
                        print(f"      Description: {signal['description']}")
                        print(f"      Estimated Profit: Rs{signal['estimated_profit']:,.0f}")
                        print()
                else:
                    print("ℹ️  No manipulation patterns detected in this cycle")
            
        else:
            print(f"❌ Failed to trigger detection cycle - HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error running detection test: {str(e)}")
    
    print()

def show_system_architecture():
    """Show system architecture information"""
    print("🏗️  SYSTEM ARCHITECTURE")
    print("-" * 40)
    print("📦 Core Components:")
    print("   • Detection Engine - Orchestrates data collection and analysis")
    print("   • NSE Data Collector - Async data collection with rate limiting")
    print("   • Spoofing Detector - Detects order spoofing patterns")
    print("   • Database Layer - SQLite/PostgreSQL with connection pooling")
    print("   • Cache Layer - Redis-based caching for performance")
    print("   • API Server - FastAPI REST interface")
    print("   • Monitoring - Prometheus metrics and structured logging")
    print()
    print("🔧 Technology Stack:")
    print("   • Python 3.11 with async/await")
    print("   • FastAPI for REST API")
    print("   • SQLAlchemy for database ORM")
    print("   • aiohttp for async HTTP requests")
    print("   • Redis for caching")
    print("   • Prometheus for metrics")
    print("   • Docker for containerization")
    print()

def main():
    """Main demonstration function"""
    print_banner()
    
    # Show architecture
    show_system_architecture()
    
    # Test API endpoints
    test_api_endpoints()
    
    # Run detection test
    run_detection_test()
    
    # Final summary
    print("🎉 DEMONSTRATION COMPLETE")
    print("-" * 40)
    print("✅ API Server: Running on http://localhost:8081")
    print("📚 Documentation: http://localhost:8081/docs")
    print("📊 Prometheus Metrics: http://localhost:8000")
    print("🔍 Detection Engine: Ready for continuous monitoring")
    print()
    print("🚀 The Options Manipulation Detection System is fully operational!")
    print("   Ready to detect manipulation patterns in real-time options trading.")
    print()

if __name__ == "__main__":
    main()
