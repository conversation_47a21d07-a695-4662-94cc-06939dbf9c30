"""
Test API functionality
"""
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_import():
    """Test API module import"""
    try:
        from api.main import app
        print("✅ API module imports successfully")
        
        # Check routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append(f"{list(route.methods)} {route.path}")
        
        print(f"📡 Found {len(routes)} API endpoints:")
        for route in routes[:10]:  # Show first 10
            print(f"   {route}")
        
        return True
    except Exception as e:
        print(f"❌ API import failed: {e}")
        return False

if __name__ == "__main__":
    success = test_api_import()
    print(f"\nAPI test result: {'✅ PASS' if success else '❌ FAIL'}")
