"""
Main detection engine that orchestrates data collection and manipulation detection
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import time

from data_collectors.multi_source_collector import multi_source_collector
from detection.base_detector import detector_registry
from detection.spoofing_detector import SpoofingDetector
from models.data_models import ManipulationSignal, OptionsData, DetectionResult
from utils.database import db_manager
from utils.cache import cache_manager
from utils.metrics import metrics_collector
from utils.enhanced_logging import enhanced_logger
from config.settings import settings
from paper_trading.paper_trader import paper_trading_engine
from utils.market_hours import market_validator
from utils.latency_tracker import latency_tracker
from utils.volatility_regime_filter import volatility_regime_filter
from utils.regime_kill_switch import regime_kill_switch

logger = logging.getLogger(__name__)

class DetectionEngine:
    """
    Main detection engine that coordinates data collection and manipulation detection
    """
    
    def __init__(self):
        self.running = False
        self.data_collector = multi_source_collector
        self.last_detection_time = None
        self.detection_stats = {
            'cycles_completed': 0,
            'total_signals': 0,
            'high_confidence_signals': 0,
            'errors': 0
        }
        
        # Initialize detectors
        self._initialize_detectors()
    
    def _initialize_detectors(self):
        """Initialize and register detection algorithms"""
        try:
            # Clear existing detectors to avoid conflicts
            detector_registry.detectors.clear()
            detector_registry.execution_order.clear()

            # Register spoofing detector
            spoofing_detector = SpoofingDetector()
            detector_registry.register(spoofing_detector, priority=10)

            # TODO: Add other detectors here
            # gamma_detector = GammaSqueezeDetector()
            # detector_registry.register(gamma_detector, priority=8)

            # volatility_detector = VolatilitySkewDetector()
            # detector_registry.register(volatility_detector, priority=6)

            logger.info(f"Initialized {len(detector_registry.list_detectors())} detectors")

        except Exception as e:
            logger.error(f"Error initializing detectors: {str(e)}")
            raise
    
    async def initialize(self):
        """Initialize the detection engine"""
        try:
            # Initialize database
            await db_manager.initialize()
            
            # Initialize cache
            await cache_manager.initialize()
            
            # Initialize multi-source data collector
            await self.data_collector.initialize()
            
            # Start metrics server
            metrics_collector.start_prometheus_server()
            
            logger.info("Detection engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize detection engine: {str(e)}")
            raise
    
    async def shutdown(self):
        """Shutdown the detection engine"""
        try:
            self.running = False
            
            # Close data collector
            if self.data_collector:
                await self.data_collector.shutdown()
            
            # Close database connections
            await db_manager.close()
            
            # Close cache connections
            await cache_manager.close()
            
            logger.info("Detection engine shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
    
    async def collect_market_data(self) -> Dict[str, List[OptionsData]]:
        """
        Collect market data from all configured symbols

        Returns:
            Dictionary mapping symbols to their options data
        """
        async with latency_tracker.measure_async('data_collection', {'symbols': settings.symbols}):
            start_time = time.time()

            try:
                # Collect data for all symbols using multi-source collector
                symbols_data = await self.data_collector.collect_all_symbols(settings.symbols)
            
                # Store data in database
                all_options_data = []
                for symbol, options_list in symbols_data.items():
                    all_options_data.extend(options_list)

                if all_options_data:
                    await db_manager.store_options_data(all_options_data)

                # Record metrics
                collection_time = time.time() - start_time
                metrics_collector.record_histogram(
                    'data_collection_duration_seconds',
                    collection_time,
                    {'source': 'multi_source'}
                )

                metrics_collector.increment_counter(
                    'data_requests_total',
                    {'source': 'multi_source', 'status': 'success'}
                )

                logger.info(
                    f"Collected {len(all_options_data)} data points from "
                    f"{len(symbols_data)} symbols in {collection_time:.2f}s"
                )

                return symbols_data

        except Exception as e:
                # Record error metrics
                metrics_collector.increment_counter(
                    'data_requests_total',
                    {'source': 'nse', 'status': 'error'}
                )

                logger.error(f"Error collecting market data: {str(e)}")
                raise
    
    async def run_detection_cycle(self) -> List[ManipulationSignal]:
        """
        Run a complete detection cycle

        Returns:
            List of detected manipulation signals
        """
        async with latency_tracker.measure_async('end_to_end', {'cycle': self.detection_stats['cycles_completed'] + 1}):
            # Check kill switch status first
            should_trade, kill_switch_reason = regime_kill_switch.should_allow_trading()
            if not should_trade:
                enhanced_logger.logger.critical(f"KILL SWITCH ACTIVE: {kill_switch_reason}")
                return []

            # Check if markets are open before proceeding
            allow_detection, reason = market_validator.should_allow_detection()
            if not allow_detection:
                enhanced_logger.logger.warning(f"Detection skipped: {reason}")
                market_status = market_validator.get_market_status()
                enhanced_logger.logger.info(f"Market status: {market_status}")
                return []

            cycle_start_time = time.time()
            all_signals = []

        try:
            self.detection_stats['cycles_completed'] += 1
            cycle_number = self.detection_stats['cycles_completed']

            enhanced_logger.logger.info("STARTING DETECTION CYCLE")
            enhanced_logger.logger.info("=" * 80)
            enhanced_logger.logger.info(f"   Cycle Number: {cycle_number}")
            enhanced_logger.logger.info(f"   Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            enhanced_logger.logger.info(f"   Symbols to Monitor: {settings.symbols}")

            # Collect fresh market data
            symbols_data = await self.collect_market_data()

            # Combine all options data for detection
            all_options_data = []
            for symbol, options_list in symbols_data.items():
                all_options_data.extend(options_list)

            if not all_options_data:
                enhanced_logger.logger.warning("WARNING: No options data available for detection")
                return all_signals

            enhanced_logger.logger.info(f"TOTAL DATA FOR ANALYSIS: {len(all_options_data)} options")

            # Update volatility regime filter and check if we should trade
            regime_metrics = volatility_regime_filter.update_market_data(all_options_data)
            should_trade, regime_reason = volatility_regime_filter.should_allow_trading()

            if not should_trade:
                enhanced_logger.logger.warning(f"REGIME FILTER: Trading disabled - {regime_reason}")
                enhanced_logger.logger.info(f"Current regime: {regime_metrics.volatility_regime}, IV: {regime_metrics.implied_volatility:.1%}")
                return all_signals

            # Show current market snapshot
            enhanced_logger.log_current_prices(all_options_data)

            # Run all detection algorithms
            enhanced_logger.logger.info("RUNNING DETECTION ALGORITHMS")
            async with latency_tracker.measure_async('detection_processing', {'data_points': len(all_options_data)}):
                detection_results = await detector_registry.run_all_detectors(all_options_data)
            
            # Collect all signals
            for result in detection_results:
                all_signals.extend(result.signals)
                
                # Record detector metrics
                metrics_collector.record_histogram(
                    'detection_duration_seconds',
                    result.execution_time,
                    {'detector_name': result.algorithm_name}
                )
                
                if result.errors:
                    metrics_collector.increment_counter(
                        'detector_errors_total',
                        {'detector_name': result.algorithm_name},
                        len(result.errors)
                    )
            
            # Store signals in database
            if all_signals:
                await db_manager.store_manipulation_signals(all_signals)
            
            # Update statistics
            self.detection_stats['total_signals'] += len(all_signals)

            high_conf_signals = [s for s in all_signals if s.confidence >= settings.detection.high_confidence_threshold]
            self.detection_stats['high_confidence_signals'] += len(high_conf_signals)

            # Record business metrics
            for signal in all_signals:
                confidence_level = signal.confidence_level.value
                if metrics_collector:
                    metrics_collector.increment_counter(
                        'signals_generated_total',
                        {
                            'pattern_type': signal.pattern_type.value,
                            'confidence_level': confidence_level
                        }
                    )

            # Record cycle completion
            cycle_time = time.time() - cycle_start_time
            if metrics_collector:
                metrics_collector.increment_counter(
                    'detection_cycles_total',
                    {'status': 'success'}
                )

            self.last_detection_time = datetime.now()

            # Log comprehensive detection summary
            detectors_run = [result.algorithm_name for result in detection_results]
            enhanced_logger.log_detection_summary(
                cycle_number, len(all_signals), len(high_conf_signals),
                cycle_time, detectors_run
            )

            # Show detailed results for each detector
            enhanced_logger.logger.info("DETECTOR RESULTS:")
            for result in detection_results:
                enhanced_logger.logger.info(f"   {result.algorithm_name}:")
                enhanced_logger.logger.info(f"     Signals: {result.signals_found}")
                enhanced_logger.logger.info(f"     Execution Time: {result.execution_time:.3f}s")
                if result.errors:
                    enhanced_logger.logger.error(f"     Errors: {result.errors}")

            # Show high confidence signals prominently
            if high_conf_signals:
                enhanced_logger.logger.warning("HIGH CONFIDENCE SIGNALS DETECTED!")
                for signal in high_conf_signals:
                    enhanced_logger.logger.warning(f"   {signal.pattern_type.value}: {signal.description}")
                    enhanced_logger.logger.warning(f"   Confidence: {signal.confidence:.1%}, Profit: Rs{signal.estimated_profit:,.0f}")

            # Process signals for paper trading
            if all_signals and all_options_data:
                enhanced_logger.logger.info(f"Processing {len(all_signals)} signals for paper trading")
                await self._process_signals_for_paper_trading(all_signals, all_options_data)

            # Update open paper trades with current market data
            if all_options_data:
                await paper_trading_engine.update_open_trades(all_options_data)

            return all_signals
            
        except Exception as e:
            self.detection_stats['errors'] += 1
            
            metrics_collector.increment_counter(
                'detection_cycles_total',
                {'status': 'error'}
            )
            
            logger.error(f"Error in detection cycle: {str(e)}")
            raise
    
    async def _process_signals_for_paper_trading(self, signals: List[ManipulationSignal], market_data: List[OptionsData]):
        """
        Process manipulation signals for paper trading

        Args:
            signals: List of manipulation signals
            market_data: Current market data for pricing
        """
        try:
            trades_executed = 0
            for signal in signals:
                # Process each signal for potential paper trade
                trade = await paper_trading_engine.process_manipulation_signal(signal, market_data)
                if trade:
                    trades_executed += 1
                    enhanced_logger.logger.info(f"Paper trade executed: {trade.action} {trade.symbol}")

            if trades_executed > 0:
                enhanced_logger.logger.info(f"Executed {trades_executed} paper trades from {len(signals)} signals")

        except Exception as e:
            logger.error(f"Error processing signals for paper trading: {str(e)}")

    async def process_high_confidence_signals(self, signals: List[ManipulationSignal]):
        """
        Process high confidence signals for immediate action
        
        Args:
            signals: List of manipulation signals
        """
        high_conf_signals = [
            s for s in signals 
            if s.confidence >= settings.detection.high_confidence_threshold
        ]
        
        if not high_conf_signals:
            return
        
        logger.warning(f"HIGH CONFIDENCE MANIPULATION DETECTED: {len(high_conf_signals)} signals")
        
        for signal in high_conf_signals:
            logger.warning(
                f"ALERT: {signal.pattern_type.value} - {signal.description} "
                f"(confidence: {signal.confidence:.2f}, profit: ₹{signal.estimated_profit:,.0f})"
            )
            
            # TODO: Implement alerting system
            # await alert_manager.send_alert(signal)
    
    async def run_continuous(self):
        """
        Run detection engine continuously
        """
        self.running = True
        logger.info("Starting continuous detection engine")
        
        try:
            while self.running:
                try:
                    # Run detection cycle
                    signals = await self.run_detection_cycle()
                    
                    # Process high confidence signals
                    await self.process_high_confidence_signals(signals)
                    
                    # Update system metrics
                    metrics_collector.update_system_metrics()
                    
                    # Store system metrics in database
                    system_stats = metrics_collector.get_summary_stats()
                    await db_manager.store_system_metrics(system_stats)
                    
                    # Wait before next cycle
                    await asyncio.sleep(settings.detection_interval)
                    
                except Exception as e:
                    logger.error(f"Error in detection cycle: {str(e)}")
                    # Wait a bit before retrying
                    await asyncio.sleep(30)
                    
        except KeyboardInterrupt:
            logger.info("Detection engine stopped by user")
        except Exception as e:
            logger.error(f"Fatal error in detection engine: {str(e)}")
            raise
        finally:
            await self.shutdown()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get detection engine statistics
        
        Returns:
            Dictionary of statistics
        """
        stats = self.detection_stats.copy()
        stats.update({
            'running': self.running,
            'last_detection_time': self.last_detection_time,
            'detector_count': len(detector_registry.list_detectors()),
            'detector_stats': detector_registry.get_all_statistics()
        })
        
        if self.data_collector:
            stats['data_collector_stats'] = self.data_collector.get_statistics()
        
        return stats

# Global detection engine instance
detection_engine = DetectionEngine()
