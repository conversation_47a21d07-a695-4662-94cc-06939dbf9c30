#!/usr/bin/env python3
"""
Multi-source data collector that tries multiple APIs for real market data
Priority: NSE API -> Yahoo Finance -> Other sources
"""
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any

from models.data_models import OptionsData
from data_collectors.nse_collector import NSEDataCollector
from data_collectors.yahoo_collector import YahooFinanceCollector
from utils.exceptions import DataCollectionError

logger = logging.getLogger(__name__)

class MultiSourceDataCollector:
    """
    Multi-source data collector that tries multiple APIs for real market data
    """
    
    def __init__(self):
        self.nse_collector = NSEDataCollector()
        self.yahoo_collector = YahooFinanceCollector()
        self.active_sources = []
        
        # Statistics
        self.collection_stats = {
            'nse_success': 0,
            'nse_failures': 0,
            'yahoo_success': 0,
            'yahoo_failures': 0,
            'total_collections': 0,
            'last_successful_source': None
        }
    
    async def initialize(self):
        """Initialize all data sources"""
        logger.info("Initializing multi-source data collector...")
        
        # Initialize NSE collector
        try:
            await self.nse_collector.start_session()
            self.active_sources.append('nse')
            logger.info("✅ NSE collector initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize NSE collector: {str(e)}")
        
        # Initialize Yahoo Finance collector
        try:
            await self.yahoo_collector.start_session()
            self.active_sources.append('yahoo')
            logger.info("✅ Yahoo Finance collector initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Yahoo Finance collector: {str(e)}")
        
        if not self.active_sources:
            raise DataCollectionError("No data sources could be initialized")
        
        logger.info(f"🚀 Multi-source collector ready with sources: {self.active_sources}")
    
    async def shutdown(self):
        """Shutdown all data sources"""
        logger.info("Shutting down multi-source data collector...")
        
        try:
            await self.nse_collector.close_session()
        except Exception as e:
            logger.error(f"Error closing NSE collector: {str(e)}")
        
        try:
            await self.yahoo_collector.close_session()
        except Exception as e:
            logger.error(f"Error closing Yahoo collector: {str(e)}")
        
        logger.info("Multi-source data collector shutdown complete")
    
    async def collect_options_data(self, symbol: str) -> List[OptionsData]:
        """
        Collect options data for a symbol, trying multiple sources
        """
        self.collection_stats['total_collections'] += 1
        
        # Try NSE first (primary source for Indian markets)
        if 'nse' in self.active_sources:
            try:
                logger.info(f"🔄 Trying NSE API for {symbol}...")
                options_data = await self.nse_collector.get_options_chain(symbol)
                
                if options_data:
                    self.collection_stats['nse_success'] += 1
                    self.collection_stats['last_successful_source'] = 'nse'
                    logger.info(f"✅ Successfully collected {len(options_data)} options from NSE for {symbol}")
                    return options_data
                    
            except Exception as e:
                self.collection_stats['nse_failures'] += 1
                logger.warning(f"❌ NSE API failed for {symbol}: {str(e)}")
        
        # Try Yahoo Finance as fallback
        if 'yahoo' in self.active_sources:
            try:
                logger.info(f"🔄 Trying Yahoo Finance for {symbol}...")
                options_data = await self.yahoo_collector.get_options_chain(symbol)
                
                if options_data:
                    self.collection_stats['yahoo_success'] += 1
                    self.collection_stats['last_successful_source'] = 'yahoo'
                    logger.info(f"✅ Successfully collected {len(options_data)} options from Yahoo Finance for {symbol}")
                    return options_data
                    
            except Exception as e:
                self.collection_stats['yahoo_failures'] += 1
                logger.warning(f"❌ Yahoo Finance failed for {symbol}: {str(e)}")
        
        # If all sources failed
        raise DataCollectionError(f"All data sources failed for {symbol}")
    
    async def collect_all_symbols(self, symbols: List[str]) -> Dict[str, List[OptionsData]]:
        """
        Collect options data for all symbols using the best available sources
        """
        logger.info(f"🔄 Collecting data for {len(symbols)} symbols: {symbols}")
        
        results = {}
        successful_collections = 0
        
        # Collect data for each symbol
        for symbol in symbols:
            try:
                options_data = await self.collect_options_data(symbol)
                results[symbol] = options_data
                successful_collections += 1
                
                # Add small delay between requests to be respectful
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"❌ Failed to collect data for {symbol}: {str(e)}")
                results[symbol] = []
        
        # Log collection summary
        total_options = sum(len(options) for options in results.values())
        logger.info(f"📊 Collection Summary:")
        logger.info(f"   Symbols processed: {len(symbols)}")
        logger.info(f"   Successful collections: {successful_collections}")
        logger.info(f"   Total options collected: {total_options}")
        logger.info(f"   Last successful source: {self.collection_stats['last_successful_source']}")
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics from all sources"""
        nse_stats = {}
        yahoo_stats = {}
        
        try:
            nse_stats = self.nse_collector.get_statistics()
        except:
            pass
        
        try:
            yahoo_stats = self.yahoo_collector.get_statistics()
        except:
            pass
        
        return {
            "multi_source_stats": self.collection_stats,
            "active_sources": self.active_sources,
            "nse_stats": nse_stats,
            "yahoo_stats": yahoo_stats,
            "success_rates": {
                "nse": (self.collection_stats['nse_success'] / 
                       max(self.collection_stats['nse_success'] + self.collection_stats['nse_failures'], 1)) * 100,
                "yahoo": (self.collection_stats['yahoo_success'] / 
                         max(self.collection_stats['yahoo_success'] + self.collection_stats['yahoo_failures'], 1)) * 100
            }
        }
    
    async def test_all_sources(self) -> Dict[str, bool]:
        """Test all data sources to see which ones are working"""
        test_results = {}
        
        # Test NSE
        if 'nse' in self.active_sources:
            try:
                test_data = await self.nse_collector.get_options_chain('NIFTY')
                test_results['nse'] = len(test_data) > 0
                logger.info(f"NSE test: {'✅ PASS' if test_results['nse'] else '❌ FAIL'}")
            except Exception as e:
                test_results['nse'] = False
                logger.info(f"NSE test: ❌ FAIL - {str(e)}")
        
        # Test Yahoo Finance
        if 'yahoo' in self.active_sources:
            try:
                test_data = await self.yahoo_collector.get_options_chain('NIFTY')
                test_results['yahoo'] = len(test_data) > 0
                logger.info(f"Yahoo Finance test: {'✅ PASS' if test_results['yahoo'] else '❌ FAIL'}")
            except Exception as e:
                test_results['yahoo'] = False
                logger.info(f"Yahoo Finance test: ❌ FAIL - {str(e)}")
        
        working_sources = [source for source, working in test_results.items() if working]
        logger.info(f"🔍 Source test complete. Working sources: {working_sources}")
        
        return test_results

# Global instance
multi_source_collector = MultiSourceDataCollector()
