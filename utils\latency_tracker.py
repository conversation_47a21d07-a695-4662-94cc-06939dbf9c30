"""
Microsecond-precision latency tracking for real-time trading systems
Critical for detecting when the system becomes too slow for manipulation detection
"""
import time
import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics
import threading
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

@dataclass
class LatencyMeasurement:
    """Single latency measurement with microsecond precision"""
    operation: str
    start_time_ns: int
    end_time_ns: int
    duration_us: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_ms(self) -> float:
        """Duration in milliseconds"""
        return self.duration_us / 1000.0
    
    @property
    def is_critical(self) -> bool:
        """Check if latency exceeds critical threshold (100ms)"""
        return self.duration_ms > 100.0

@dataclass
class LatencyStats:
    """Statistical summary of latency measurements"""
    operation: str
    count: int
    mean_us: float
    median_us: float
    p95_us: float
    p99_us: float
    max_us: float
    min_us: float
    critical_count: int
    
    @property
    def mean_ms(self) -> float:
        return self.mean_us / 1000.0
    
    @property
    def p95_ms(self) -> float:
        return self.p95_us / 1000.0
    
    @property
    def critical_rate(self) -> float:
        """Percentage of measurements exceeding 100ms"""
        return (self.critical_count / max(self.count, 1)) * 100.0

class LatencyTracker:
    """
    High-precision latency tracker for real-time trading systems
    Tracks every operation from data collection to trade execution
    """
    
    def __init__(self, max_measurements_per_operation: int = 10000):
        self.max_measurements = max_measurements_per_operation
        self.measurements: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_measurements_per_operation))
        self.active_operations: Dict[str, int] = {}
        self.lock = threading.RLock()
        
        # Critical thresholds (microseconds)
        self.critical_thresholds = {
            'data_collection': 50_000,      # 50ms for data collection
            'detection_processing': 30_000,  # 30ms for detection
            'signal_generation': 10_000,     # 10ms for signal generation
            'trade_execution': 5_000,        # 5ms for trade execution
            'end_to_end': 100_000,          # 100ms total pipeline
        }
        
        # Performance alerts
        self.alert_callbacks: List[callable] = []
        
    def start_operation(self, operation: str, metadata: Dict[str, Any] = None) -> str:
        """
        Start timing an operation
        
        Args:
            operation: Name of the operation being timed
            metadata: Additional context for the operation
            
        Returns:
            Unique operation ID for this timing session
        """
        operation_id = f"{operation}_{time.time_ns()}"
        start_time = time.time_ns()
        
        with self.lock:
            self.active_operations[operation_id] = start_time
            
        return operation_id
    
    def end_operation(self, operation_id: str, metadata: Dict[str, Any] = None) -> Optional[LatencyMeasurement]:
        """
        End timing an operation and record the measurement
        
        Args:
            operation_id: ID returned from start_operation
            metadata: Additional metadata to store with measurement
            
        Returns:
            LatencyMeasurement if operation was found, None otherwise
        """
        end_time = time.time_ns()
        
        with self.lock:
            if operation_id not in self.active_operations:
                logger.warning(f"Unknown operation ID: {operation_id}")
                return None
                
            start_time = self.active_operations.pop(operation_id)
            
        # Extract operation name from ID
        operation = operation_id.rsplit('_', 1)[0]
        duration_ns = end_time - start_time
        duration_us = duration_ns / 1000.0
        
        measurement = LatencyMeasurement(
            operation=operation,
            start_time_ns=start_time,
            end_time_ns=end_time,
            duration_us=duration_us,
            metadata=metadata or {}
        )
        
        # Store measurement
        with self.lock:
            self.measurements[operation].append(measurement)
        
        # Check for critical latency
        if measurement.is_critical:
            self._trigger_latency_alert(measurement)
            
        return measurement
    
    @asynccontextmanager
    async def measure_async(self, operation: str, metadata: Dict[str, Any] = None):
        """
        Async context manager for measuring operation latency
        
        Usage:
            async with latency_tracker.measure_async('data_collection'):
                data = await collect_data()
        """
        operation_id = self.start_operation(operation, metadata)
        try:
            yield
        finally:
            self.end_operation(operation_id, metadata)
    
    def measure_sync(self, operation: str, metadata: Dict[str, Any] = None):
        """
        Synchronous context manager for measuring operation latency
        
        Usage:
            with latency_tracker.measure_sync('calculation'):
                result = expensive_calculation()
        """
        return _SyncLatencyContext(self, operation, metadata)
    
    def get_stats(self, operation: str) -> Optional[LatencyStats]:
        """
        Get statistical summary for an operation
        
        Args:
            operation: Name of the operation
            
        Returns:
            LatencyStats if measurements exist, None otherwise
        """
        with self.lock:
            if operation not in self.measurements or not self.measurements[operation]:
                return None
                
            measurements = list(self.measurements[operation])
        
        durations = [m.duration_us for m in measurements]
        critical_count = sum(1 for m in measurements if m.is_critical)
        
        return LatencyStats(
            operation=operation,
            count=len(durations),
            mean_us=statistics.mean(durations),
            median_us=statistics.median(durations),
            p95_us=self._percentile(durations, 95),
            p99_us=self._percentile(durations, 99),
            max_us=max(durations),
            min_us=min(durations),
            critical_count=critical_count
        )
    
    def get_all_stats(self) -> Dict[str, LatencyStats]:
        """Get stats for all tracked operations"""
        with self.lock:
            operations = list(self.measurements.keys())
        
        return {op: self.get_stats(op) for op in operations if self.get_stats(op)}
    
    def get_recent_critical_events(self, minutes: int = 5) -> List[LatencyMeasurement]:
        """Get recent measurements that exceeded critical thresholds"""
        cutoff_time = time.time_ns() - (minutes * 60 * 1_000_000_000)
        critical_events = []
        
        with self.lock:
            for operation, measurements in self.measurements.items():
                for measurement in measurements:
                    if (measurement.start_time_ns >= cutoff_time and 
                        measurement.is_critical):
                        critical_events.append(measurement)
        
        return sorted(critical_events, key=lambda x: x.start_time_ns, reverse=True)
    
    def is_system_healthy(self) -> bool:
        """
        Check if system latency is within acceptable bounds
        
        Returns:
            True if system is performing within thresholds
        """
        # Check recent critical events
        recent_critical = self.get_recent_critical_events(minutes=1)
        if len(recent_critical) > 5:  # More than 5 critical events in 1 minute
            return False
        
        # Check key operation performance
        key_operations = ['data_collection', 'detection_processing', 'end_to_end']
        for operation in key_operations:
            stats = self.get_stats(operation)
            if stats and stats.critical_rate > 10.0:  # More than 10% critical
                return False
        
        return True
    
    def add_alert_callback(self, callback: callable):
        """Add callback function for latency alerts"""
        self.alert_callbacks.append(callback)
    
    def _trigger_latency_alert(self, measurement: LatencyMeasurement):
        """Trigger alerts for critical latency measurements"""
        logger.warning(
            f"CRITICAL LATENCY: {measurement.operation} took {measurement.duration_ms:.2f}ms "
            f"(threshold: {self.critical_thresholds.get(measurement.operation, 100000) / 1000:.1f}ms)"
        )
        
        for callback in self.alert_callbacks:
            try:
                callback(measurement)
            except Exception as e:
                logger.error(f"Error in latency alert callback: {e}")
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = (percentile / 100.0) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def reset_measurements(self, operation: str = None):
        """Reset measurements for an operation or all operations"""
        with self.lock:
            if operation:
                if operation in self.measurements:
                    self.measurements[operation].clear()
            else:
                self.measurements.clear()
                self.active_operations.clear()

class _SyncLatencyContext:
    """Synchronous context manager for latency measurement"""
    
    def __init__(self, tracker: LatencyTracker, operation: str, metadata: Dict[str, Any] = None):
        self.tracker = tracker
        self.operation = operation
        self.metadata = metadata
        self.operation_id = None
    
    def __enter__(self):
        self.operation_id = self.tracker.start_operation(self.operation, self.metadata)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.operation_id:
            self.tracker.end_operation(self.operation_id, self.metadata)

# Global latency tracker instance
latency_tracker = LatencyTracker()
