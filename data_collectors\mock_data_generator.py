#!/usr/bin/env python3
"""
Mock data generator for testing and demonstration purposes
Generates realistic options data when live APIs are unavailable
"""
import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import math

from models.data_models import OptionsData, OptionType

logger = logging.getLogger(__name__)

class MockDataGenerator:
    """
    Generates realistic mock options data for testing and demonstration
    """
    
    def __init__(self):
        self.base_prices = {
            'NIFTY': 24500.0,
            'BANKNIFTY': 52000.0,
            'FINNIFTY': 22000.0
        }
        self.volatility = 0.15  # 15% annual volatility
        self.risk_free_rate = 0.06  # 6% risk-free rate
        
    def generate_options_chain(self, symbol: str, num_strikes: int = 10) -> List[OptionsData]:
        """
        Generate a realistic options chain for a symbol
        
        Args:
            symbol: Symbol to generate data for (NIFTY, BANKNIFTY, FINNIFTY)
            num_strikes: Number of strike prices to generate
            
        Returns:
            List of OptionsData objects
        """
        if symbol not in self.base_prices:
            logger.warning(f"Unknown symbol {symbol}, using NIFTY defaults")
            symbol = 'NIFTY'
        
        spot_price = self.base_prices[symbol]
        options_data = []
        
        # Generate strikes around current price
        strike_interval = 100 if symbol == 'NIFTY' else 500 if symbol == 'BANKNIFTY' else 100
        start_strike = int((spot_price - (num_strikes // 2) * strike_interval) / strike_interval) * strike_interval
        
        expiry_date = self._get_next_expiry()
        time_to_expiry = (expiry_date - datetime.now()).days / 365.0
        
        for i in range(num_strikes):
            strike = start_strike + (i * strike_interval)
            
            # Generate call option
            call_data = self._generate_option_data(
                symbol, strike, OptionType.CE, spot_price, time_to_expiry, expiry_date
            )
            options_data.append(call_data)
            
            # Generate put option
            put_data = self._generate_option_data(
                symbol, strike, OptionType.PE, spot_price, time_to_expiry, expiry_date
            )
            options_data.append(put_data)
        
        # Add some manipulation patterns for demonstration
        options_data = self._add_manipulation_patterns(options_data)
        
        logger.info(f"Generated {len(options_data)} mock options for {symbol}")
        return options_data
    
    def _generate_option_data(self, symbol: str, strike: float, option_type: OptionType, 
                            spot_price: float, time_to_expiry: float, expiry_date: datetime) -> OptionsData:
        """Generate realistic option data using Black-Scholes approximation"""
        
        # Calculate theoretical price using simplified Black-Scholes
        theoretical_price = self._black_scholes_price(
            spot_price, strike, time_to_expiry, self.risk_free_rate, self.volatility, option_type
        )
        
        # Add some randomness to simulate market conditions
        price_noise = random.uniform(-0.1, 0.1) * theoretical_price
        last_price = max(0.05, theoretical_price + price_noise)
        
        # Generate bid-ask spread (typically 2-5% of price)
        spread_percent = random.uniform(0.02, 0.05)
        spread = last_price * spread_percent
        bid_price = max(0.05, last_price - spread/2)
        ask_price = last_price + spread/2
        
        # Generate volume and open interest
        moneyness = abs(spot_price - strike) / spot_price
        volume_factor = max(0.1, 1.0 - moneyness * 2)  # Higher volume for ATM options
        
        volume = int(random.uniform(100, 2000) * volume_factor)
        open_interest = int(random.uniform(1000, 10000) * volume_factor)
        
        # Generate bid/ask quantities
        bid_qty = random.randint(50, 500)
        ask_qty = random.randint(50, 500)
        
        return OptionsData(
            symbol=symbol,
            expiry_date=expiry_date,
            strike=strike,
            option_type=option_type,
            last_price=round(last_price, 2),
            bid_price=round(bid_price, 2),
            ask_price=round(ask_price, 2),
            volume=volume,
            open_interest=open_interest,
            bid_qty=bid_qty,
            ask_qty=ask_qty,
            timestamp=datetime.now()
        )
    
    def _black_scholes_price(self, S: float, K: float, T: float, r: float, sigma: float, 
                           option_type: OptionType) -> float:
        """Simplified Black-Scholes option pricing"""
        if T <= 0:
            # Option expired
            if option_type == OptionType.CE:
                return max(0, S - K)
            else:
                return max(0, K - S)
        
        try:
            d1 = (math.log(S/K) + (r + 0.5*sigma**2)*T) / (sigma*math.sqrt(T))
            d2 = d1 - sigma*math.sqrt(T)
            
            # Simplified normal CDF approximation
            def norm_cdf(x):
                return 0.5 * (1 + math.erf(x / math.sqrt(2)))
            
            if option_type == OptionType.CE:
                price = S * norm_cdf(d1) - K * math.exp(-r*T) * norm_cdf(d2)
            else:
                price = K * math.exp(-r*T) * norm_cdf(-d2) - S * norm_cdf(-d1)
            
            return max(0.05, price)  # Minimum price of 0.05
            
        except (ValueError, OverflowError):
            # Fallback for edge cases
            intrinsic = max(0, S - K) if option_type == OptionType.CE else max(0, K - S)
            time_value = random.uniform(0.1, 5.0) * T
            return intrinsic + time_value
    
    def _get_next_expiry(self) -> datetime:
        """Get next Thursday (typical options expiry)"""
        today = datetime.now()
        days_ahead = 3 - today.weekday()  # Thursday is 3
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        return today + timedelta(days=days_ahead)
    
    def _add_manipulation_patterns(self, options_data: List[OptionsData]) -> List[OptionsData]:
        """Add manipulation patterns to some options for demonstration"""
        
        # Select a few options to add manipulation patterns
        manipulation_count = min(3, len(options_data) // 4)
        manipulation_indices = random.sample(range(len(options_data)), manipulation_count)
        
        for idx in manipulation_indices:
            option = options_data[idx]
            
            # Add manipulation pattern (unusual bid/ask quantities)
            manipulation_type = random.choice(['bid_spoofing', 'ask_spoofing', 'coordinated'])
            
            if manipulation_type == 'bid_spoofing':
                # Artificially high bid quantity
                option.bid_qty = random.randint(2000, 5000)
                option.bid_price = option.last_price + random.uniform(0.5, 2.0)
                
            elif manipulation_type == 'ask_spoofing':
                # Artificially high ask quantity
                option.ask_qty = random.randint(2000, 5000)
                option.ask_price = option.last_price - random.uniform(0.5, 2.0)
                
            else:  # coordinated
                # Both bid and ask manipulation
                option.bid_qty = random.randint(1500, 3000)
                option.ask_qty = random.randint(1500, 3000)
                option.volume = option.volume * 3  # Unusual volume spike
        
        return options_data
    
    def generate_multiple_symbols(self, symbols: List[str]) -> Dict[str, List[OptionsData]]:
        """Generate options data for multiple symbols"""
        result = {}
        
        for symbol in symbols:
            try:
                result[symbol] = self.generate_options_chain(symbol)
            except Exception as e:
                logger.error(f"Error generating mock data for {symbol}: {str(e)}")
                result[symbol] = []
        
        return result

# Global instance
mock_data_generator = MockDataGenerator()
